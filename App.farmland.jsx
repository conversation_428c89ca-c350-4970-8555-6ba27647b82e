import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import Header from './src/components/Header'
import Footer from './src/components/Footer'
import Home from './src/pages/Home'
import Projects from './src/pages/Projects'
import Contact from './src/pages/Contact'
import Gallery from './src/pages/Gallery'
import Blog from './src/pages/Blog'
import Enquiry from './src/pages/Enquiry'

function App() {
  return (
    <Router>
      <div className="App">
        <Header />
        <main>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/projects" element={<Projects />} />
            <Route path="/gallery" element={<Gallery />} />
            <Route path="/blog" element={<Blog />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/enquiry" element={<Enquiry />} />
            <Route path="*" element={<Home />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  )
}

export default App