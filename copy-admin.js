/**
 * Simple script to copy the admin directory to the dist folder
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Source and destination directories
const sourceDir = path.join(__dirname, 'public', 'admin');
const destDir = path.join(__dirname, 'dist', 'admin');

// Create the destination directory if it doesn't exist
if (!fs.existsSync(destDir)) {
  fs.mkdirSync(destDir, { recursive: true });
  console.log(`Created directory: ${destDir}`);
}

// Copy all files from the source directory to the destination directory
const copyFiles = (src, dest) => {
  const files = fs.readdirSync(src);

  files.forEach(file => {
    const srcPath = path.join(src, file);
    const destPath = path.join(dest, file);

    const stat = fs.statSync(srcPath);

    if (stat.isDirectory()) {
      // If it's a directory, create it in the destination and copy its contents
      if (!fs.existsSync(destPath)) {
        fs.mkdirSync(destPath, { recursive: true });
      }
      copyFiles(srcPath, destPath);
    } else {
      // If it's a file, copy it to the destination
      fs.copyFileSync(srcPath, destPath);
      console.log(`Copied: ${srcPath} -> ${destPath}`);
    }
  });
};

try {
  // Check if the source directory exists
  if (fs.existsSync(sourceDir)) {
    copyFiles(sourceDir, destDir);
    console.log('Admin directory copied successfully!');
  } else {
    console.log(`Source directory ${sourceDir} does not exist. Creating basic admin files.`);

    // Create basic admin files
    const indexHtml = `<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Darvi Group CMS</title>
    <!-- Include the script that enables Netlify Identity -->
    <script src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap">
  </head>
  <body>
    <!-- Include the script that builds the page and powers Decap CMS (formerly Netlify CMS) -->
    <script src="https://unpkg.com/decap-cms@^3.0.0/dist/decap-cms.js"></script>
    <script>
      // Initialize the CMS
      if (window.netlifyIdentity) {
        window.netlifyIdentity.on("init", user => {
          if (!user) {
            window.netlifyIdentity.on("login", () => {
              document.location.href = "/admin/";
            });
          }
        });
      }
    </script>
  </body>
</html>`;

    const configYml = `backend:
  name: git-gateway
  branch: master # Branch to update (using master as per your repository)

# Publish mode options:
# - editorial_workflow: Adds an interface for drafting, reviewing, and approving content
publish_mode: editorial_workflow

# Media folder where uploaded images will be stored in the repo
media_folder: "public/images/uploads"
# Public folder where the images will be accessed in the built site
public_folder: "/images/uploads"

collections:
  # Home Page
  - name: "home"
    label: "Home Page"
    files:
      - name: "home"
        label: "Home Page Content"
        file: "content/home.json"
        fields:
          - { label: "Hero Title", name: "heroTitle", widget: "string" }
          - { label: "Hero Subtitle", name: "heroSubtitle", widget: "string" }
          - { label: "Primary Button Text", name: "primaryBtnText", widget: "string" }
          - { label: "Primary Button Link", name: "primaryBtnLink", widget: "string" }`;

    // Write the files
    fs.writeFileSync(path.join(destDir, 'index.html'), indexHtml);
    console.log(`Created admin/index.html`);

    fs.writeFileSync(path.join(destDir, 'config.yml'), configYml);
    console.log(`Created admin/config.yml`);
  }
} catch (error) {
  console.error('Error copying admin directory:', error);
  // Don't exit with error code, just log the error
  console.log('Continuing build process despite admin copy error...');
}
