import { Link } from 'react-router-dom'
import { FaFacebook, FaTwitter, FaInstagram, FaLinkedin, FaYoutube, FaLeaf, FaPhone, FaEnvelope, FaMapMarkerAlt } from 'react-icons/fa'
import styled from 'styled-components'

const StyledFooter = styled.footer`
  background-color: var(--primary-dark);
  color: var(--text-light);
  padding: 4rem 0 2rem;
`

const FooterContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;

  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  @media (max-width: 576px) {
    gap: 1.5rem;
  }
`

const FooterColumn = styled.div`
  display: flex;
  flex-direction: column;
`

const FooterLogo = styled(Link)`
  display: flex;
  align-items: center;
  font-family: var(--font-heading);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-light);
  margin-bottom: 1rem;

  .logo-icon {
    height: 32px;
    width: auto;
    margin-right: 0.5rem;
    filter: brightness(0) invert(1);
  }

  svg {
    margin-right: 0.5rem;
    color: var(--accent-color);
  }

  @media (max-width: 768px) {
    justify-content: center;
    font-size: 1.3rem;
  }

  @media (max-width: 480px) {
    font-size: 1.2rem;
  }
`

const FooterHeading = styled.h4`
  color: var(--text-light);
  margin-bottom: 1.5rem;
  position: relative;

  &:after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--accent-color);
  }
`

const FooterLink = styled(Link)`
  color: var(--text-light);
  margin-bottom: 0.75rem;
  transition: color 0.3s ease;

  &:hover {
    color: var(--accent-color);
  }
`

const FooterText = styled.p`
  margin-bottom: 1rem;
`

const ContactItem = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1rem;

  svg {
    margin-right: 0.75rem;
    color: var(--accent-color);
  }
`

const SocialLinks = styled.div`
  display: flex;
  margin-top: 1rem;
`

const SocialLink = styled.a`
  color: var(--text-light);
  font-size: 1.25rem;
  margin-right: 1rem;
  transition: color 0.3s ease;

  &:hover {
    color: var(--accent-color);
  }
`

const Copyright = styled.div`
  text-align: center;
  margin-top: 3rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
`

function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <StyledFooter>
      <div className="container">
        <FooterContainer>
          <FooterColumn>
            <FooterLogo to="/">
              <img src="/darvi-icon.png" alt="Darvi Farmlands" className="logo-icon" />
              Darvi Farmlands
            </FooterLogo>
            <FooterText>
              Your trusted partner in sustainable farmland development and agricultural project management since 2018.
            </FooterText>
            <SocialLinks>
              <SocialLink href="#" target="_blank" rel="noopener noreferrer">
                <FaFacebook />
              </SocialLink>
              <SocialLink href="#" target="_blank" rel="noopener noreferrer">
                <FaTwitter />
              </SocialLink>
              <SocialLink href="#" target="_blank" rel="noopener noreferrer">
                <FaInstagram />
              </SocialLink>
              <SocialLink href="#" target="_blank" rel="noopener noreferrer">
                <FaLinkedin />
              </SocialLink>
              <SocialLink href="#" target="_blank" rel="noopener noreferrer">
                <FaYoutube />
              </SocialLink>
            </SocialLinks>
          </FooterColumn>

          <FooterColumn>
            <FooterHeading>Quick Links</FooterHeading>
            <FooterLink to="/">Home</FooterLink>
            <FooterLink to="/projects">Projects</FooterLink>
            <FooterLink to="/contact">Contact Us</FooterLink>
          </FooterColumn>

          <FooterColumn>
            <FooterHeading>Project Categories</FooterHeading>
            <FooterLink to="/projects?filter=ongoing">Ongoing Projects</FooterLink>
            <FooterLink to="/projects?filter=completed">Completed Projects</FooterLink>
            <FooterLink to="/projects?filter=upcoming">Upcoming Projects</FooterLink>
          </FooterColumn>

          <FooterColumn>
            <FooterHeading>Contact Info</FooterHeading>
            <ContactItem>
              <FaMapMarkerAlt />
              <span>#2 Totad building, Arjun Vihar Gokul road Hubli, Karnataka 580030, India</span>
            </ContactItem>
            <ContactItem>
              <FaPhone />
              <span>+91 99868 90777</span>
            </ContactItem>
            <ContactItem>
              <FaEnvelope />
              <span><EMAIL></span>
            </ContactItem>
          </FooterColumn>
        </FooterContainer>

        <Copyright>
          <p>&copy; {currentYear} Darvi Group. All rights reserved.</p>
        </Copyright>
      </div>
    </StyledFooter>
  )
}

export default Footer
