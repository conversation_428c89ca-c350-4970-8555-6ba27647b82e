import styled from 'styled-components'
import { motion } from 'framer-motion'

const Divider<PERSON>ontainer = styled(motion.div)`
  position: relative;
  width: 100%;
  height: 80px;
  overflow: hidden;
  
  @media (max-width: 768px) {
    height: 60px;
  }
`

const CurvedSVG = styled.svg`
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  fill: ${props => props.fillColor || 'var(--primary-color)'};
  opacity: ${props => props.opacity || 1};
`

const curves = {
  wave1: "M0,32L48,37.3C96,43,192,53,288,58.7C384,64,480,64,576,58.7C672,53,768,43,864,42.7C960,43,1056,53,1152,58.7C1248,64,1344,64,1392,64L1440,64L1440,80L1392,80C1344,80,1248,80,1152,80C1056,80,960,80,864,80C768,80,672,80,576,80C480,80,384,80,288,80C192,80,96,80,48,80L0,80Z",
  wave2: "M0,48L48,42.7C96,37,192,27,288,26.7C384,27,480,37,576,42.7C672,48,768,48,864,45.3C960,43,1056,37,1152,37.3C1248,37,1344,43,1392,45.3L1440,48L1440,80L1392,80C1344,80,1248,80,1152,80C1056,80,960,80,864,80C768,80,672,80,576,80C480,80,384,80,288,80C192,80,96,80,48,80L0,80Z",
  wave3: "M0,64L48,58.7C96,53,192,43,288,37.3C384,32,480,32,576,37.3C672,43,768,53,864,56C960,59,1056,53,1152,48C1248,43,1344,37,1392,34.7L1440,32L1440,80L1392,80C1344,80,1248,80,1152,80C1056,80,960,80,864,80C768,80,672,80,576,80C480,80,384,80,288,80C192,80,96,80,48,80L0,80Z",
  simple: "M0,40L1440,60L1440,80L0,80Z"
}

function CurvedDivider({ 
  type = "wave1", 
  fillColor = "var(--primary-color)", 
  opacity = 0.1,
  direction = "bottom",
  className = ""
}) {
  const pathData = curves[type] || curves.wave1
  
  const transform = direction === "top" ? "rotate(180)" : "rotate(0)"
  const transformOrigin = "center"

  return (
    <DividerContainer 
      className={className}
      initial={{ opacity: 0, scaleX: 0 }}
      whileInView={{ opacity: 1, scaleX: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 1, ease: "easeOut" }}
    >
      <CurvedSVG 
        viewBox="0 0 1440 80" 
        fillColor={fillColor} 
        opacity={opacity}
        style={{ transform, transformOrigin }}
      >
        <path d={pathData} />
      </CurvedSVG>
    </DividerContainer>
  )
}

export default CurvedDivider