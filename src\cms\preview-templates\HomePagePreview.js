import React from 'react';
import { StyleSheetInjector } from '../cms.jsx';
import styled from 'styled-components';

const PreviewContainer = styled.div`
  font-family: 'Montserrat', sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
`;

const HeroPreview = styled.div`
  background-color: #f5f5f5;
  background-image: ${props => props.backgroundImage ? `url(${props.backgroundImage})` : 'none'};
  background-size: cover;
  background-position: center;
  padding: 100px 20px;
  text-align: center;
  color: white;
  border-radius: 8px;
  margin-bottom: 30px;
`;

const HeroTitle = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 1rem;
`;

const HeroSubtitle = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
`;

const SectionTitle = styled.div`
  text-align: center;
  margin-bottom: 30px;
`;

const SectionSubtitle = styled.p`
  color: #4caf50;
  text-transform: uppercase;
  font-weight: 600;
  margin-bottom: 0.5rem;
`;

const SectionHeading = styled.h2`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const SectionDescription = styled.p`
  max-width: 700px;
  margin: 0 auto;
  opacity: 0.8;
`;

const FeaturedGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const FeaturedCard = styled.div`
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const CardImage = styled.div`
  height: 200px;
  background-image: ${props => props.image ? `url(${props.image})` : 'none'};
  background-size: cover;
  background-position: center;
`;

const CardContent = styled.div`
  padding: 20px;
`;

const CardTitle = styled.h3`
  margin-bottom: 10px;
`;

const CardDescription = styled.p`
  font-size: 0.9rem;
  opacity: 0.8;
`;

const StatusBadge = styled.span`
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 10px;
  background-color: ${props => {
    switch(props.status?.toLowerCase()) {
      case 'upcoming': return '#ffc107';
      case 'active': return '#4caf50';
      case 'managed': return '#2196f3';
      case 'completed': return '#9c27b0';
      default: return '#e0e0e0';
    }
  }};
  color: white;
`;

const HomePagePreview = ({ entry }) => {
  const data = entry.getIn(['data']).toJS();

  return (
    <StyleSheetInjector>
      <PreviewContainer>
        <h2>Home Page Preview</h2>

        <HeroPreview backgroundImage={data.heroBackgroundImage}>
          <HeroTitle>{data.heroTitle || 'Hero Title'}</HeroTitle>
          <HeroSubtitle>{data.heroSubtitle || 'Hero Subtitle'}</HeroSubtitle>
          <div>
            <button>{data.primaryBtnText || 'Primary Button'}</button>
            {data.secondaryBtnText && (
              <button style={{ marginLeft: '10px' }}>{data.secondaryBtnText}</button>
            )}
          </div>
        </HeroPreview>

        {data.farmProjectsSection && (
          <div>
            <SectionTitle>
              <SectionSubtitle>{data.farmProjectsSection.subtitle || 'Subtitle'}</SectionSubtitle>
              <SectionHeading>{data.farmProjectsSection.title || 'Title'}</SectionHeading>
              <SectionDescription>{data.farmProjectsSection.description || 'Description'}</SectionDescription>
            </SectionTitle>

            <FeaturedGrid>
              {(data.featuredProjects || []).map((project, index) => (
                <FeaturedCard key={index}>
                  <CardImage image={project.image} />
                  <CardContent>
                    {project.status && <StatusBadge status={project.status}>{project.status}</StatusBadge>}
                    <CardTitle>{project.title || `Project ${index + 1}`}</CardTitle>
                    <CardDescription>{project.description || 'Project description'}</CardDescription>
                  </CardContent>
                </FeaturedCard>
              ))}
            </FeaturedGrid>
          </div>
        )}

        {/* More sections would be added here */}
      </PreviewContainer>
    </StyleSheetInjector>
  );
};

export default HomePagePreview;
