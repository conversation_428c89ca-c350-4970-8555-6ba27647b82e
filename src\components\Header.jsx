import { useState, useEffect } from 'react'
import { Link, NavLink } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { FaBars, FaTimes, FaChevronDown } from 'react-icons/fa'
import styled from 'styled-components'

const StyledHeader = styled.header`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background: ${props => props.scrolled ? 
    'rgba(255, 255, 255, 0.95)' : 
    'rgba(255, 255, 255, 0.1)'};
  backdrop-filter: blur(20px);
  border-bottom: ${props => props.scrolled ? 
    '1px solid rgba(74, 124, 89, 0.1)' : 
    '1px solid rgba(255, 255, 255, 0.1)'};
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  padding: ${props => props.scrolled ? '0.75rem 0' : '1.25rem 0'};
  
  @media (max-width: 768px) {
    padding: ${props => props.scrolled ? '0.5rem 0' : '1rem 0'};
  }
`

const NavContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;

  @media (max-width: 768px) {
    padding: 0 1rem;
  }
`

const Logo = styled(Link)`
  display: flex;
  align-items: center;
  font-family: var(--font-heading);
  font-size: 1.6rem;
  font-weight: 700;
  color: ${props => props.scrolled ? 'var(--primary-dark)' : 'var(--primary-dark)'};
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &:hover {
    transform: scale(1.05);
  }

  .logo-icon {
    height: 45px;
    width: auto;
    margin-right: 0.75rem;
    filter: drop-shadow(0 4px 8px rgba(74, 124, 89, 0.2));
    transition: all 0.3s ease;
  }

  .logo-text {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    
    @media (max-width: 480px) {
      font-size: 1.2rem;
    }
  }

  @media (max-width: 768px) {
    font-size: 1.4rem;
    
    .logo-icon {
      height: 40px;
    }
  }
`

const NavMenu = styled.nav`
  display: flex;
  align-items: center;
  gap: 0.5rem;

  @media (max-width: 968px) {
    position: fixed;
    top: 0;
    right: ${props => props.isOpen ? '0' : '-100%'};
    width: 85%;
    max-width: 350px;
    height: 100vh;
    flex-direction: column;
    justify-content: flex-start;
    padding-top: 6rem;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(30px);
    box-shadow: -4px 0 20px rgba(74, 124, 89, 0.15);
    transition: right 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1001;
    gap: 0;
  }

  @media (max-width: 480px) {
    width: 90%;
  }
`

const NavItem = styled(NavLink)`
  margin: 0 0.75rem;
  padding: 0.75rem 1.25rem;
  color: var(--text-dark);
  font-weight: 500;
  position: relative;
  border-radius: 50px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.95rem;
  letter-spacing: 0.025em;

  &:before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  &:hover {
    color: white;
    transform: translateY(-2px);
    
    &:before {
      opacity: 1;
    }
  }

  &.active {
    color: white;
    
    &:before {
      opacity: 1;
    }
  }

  @media (max-width: 968px) {
    margin: 0.75rem 0;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    width: 80%;
    text-align: center;
    border-radius: 12px;

    &:hover {
      transform: translateX(8px);
    }
  }
`

const MobileMenuButton = styled(motion.button)`
  display: none;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(74, 124, 89, 0.2);
  border-radius: 12px;
  width: 48px;
  height: 48px;
  cursor: pointer;
  color: var(--primary-color);
  z-index: 1002;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &:hover {
    background: rgba(255, 255, 255, 1);
    border-color: var(--primary-color);
    transform: scale(1.05);
  }

  &:before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover:before {
    opacity: 0.1;
  }

  svg {
    position: relative;
    z-index: 1;
    font-size: 1.2rem;
  }

  @media (max-width: 968px) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
`

const CloseButton = styled(motion.button)`
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  background: rgba(74, 124, 89, 0.1);
  border: 2px solid rgba(74, 124, 89, 0.2);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  color: var(--primary-color);
  display: none;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
  }

  @media (max-width: 968px) {
    display: flex;
  }
`

const Overlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
  z-index: 1000;

  @media (min-width: 969px) {
    display: none;
  }
`

const DropdownMenu = styled(motion.div)`
  position: absolute;
  top: 100%;
  left: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(74, 124, 89, 0.15);
  border: 1px solid rgba(74, 124, 89, 0.1);
  min-width: 180px;
  overflow: hidden;
  z-index: 1001;

  @media (max-width: 968px) {
    position: static;
    background: transparent;
    box-shadow: none;
    border: none;
    backdrop-filter: none;
  }
`

const DropdownItem = styled(NavLink)`
  display: block;
  padding: 0.75rem 1.25rem;
  color: var(--text-dark);
  font-size: 0.9rem;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(74, 124, 89, 0.05);

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: rgba(74, 124, 89, 0.05);
    color: var(--primary-color);
    transform: translateX(4px);
  }

  @media (max-width: 968px) {
    padding: 0.75rem 2.5rem;
    border-bottom: 1px solid rgba(74, 124, 89, 0.1);
    
    &:hover {
      background: rgba(74, 124, 89, 0.1);
      transform: translateX(8px);
    }
  }
`

const NavItemWithDropdown = styled.div`
  position: relative;
  display: flex;
  align-items: center;

  @media (max-width: 968px) {
    flex-direction: column;
    width: 80%;
  }
`

const DropdownTrigger = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0.75rem;
  padding: 0.75rem 1.25rem;
  background: none;
  border: none;
  color: var(--text-dark);
  font-weight: 500;
  border-radius: 50px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.95rem;
  letter-spacing: 0.025em;
  cursor: pointer;
  position: relative;

  &:before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  &:hover {
    color: white;
    transform: translateY(-2px);
    
    &:before {
      opacity: 1;
    }
  }

  svg {
    transition: transform 0.3s ease;
    font-size: 0.8rem;
  }

  &.open svg {
    transform: rotate(180deg);
  }

  @media (max-width: 968px) {
    margin: 0.75rem 0;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    width: 100%;
    text-align: center;
    border-radius: 12px;
    justify-content: center;

    &:hover {
      transform: translateX(8px);
    }
  }
`

function Header() {
  const [scrolled, setScrolled] = useState(false)
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true)
      } else {
        setScrolled(false)
      }
    }

    window.addEventListener('scroll', handleScroll)

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const closeMenu = () => {
    setIsMenuOpen(false)
  }

  return (
    <StyledHeader scrolled={scrolled}>
      <NavContainer>
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Logo to="/" scrolled={scrolled}>
            <img src="/darvi-images/darvi-logo.png" alt="Darvi Farmlands" className="logo-icon" />
            <span className="logo-text">Darvi Farmlands</span>
          </Logo>
        </motion.div>

        <MobileMenuButton
          onClick={toggleMenu}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <motion.div
            animate={{ rotate: isMenuOpen ? 90 : 0 }}
            transition={{ duration: 0.3 }}
          >
            {isMenuOpen ? <FaTimes /> : <FaBars />}
          </motion.div>
        </MobileMenuButton>

        <AnimatePresence>
          {isMenuOpen && (
            <Overlay
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              onClick={closeMenu}
            />
          )}
        </AnimatePresence>

        <NavMenu isOpen={isMenuOpen}>
          <CloseButton
            onClick={closeMenu}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <FaTimes />
          </CloseButton>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            <NavItem to="/" onClick={closeMenu}>Home</NavItem>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: 0.15 }}
          >
            <NavItem to="/projects" onClick={closeMenu}>Projects</NavItem>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: 0.2 }}
          >
            <NavItem to="/gallery" onClick={closeMenu}>Gallery</NavItem>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: 0.25 }}
          >
            <NavItem to="/blog" onClick={closeMenu}>Blog</NavItem>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: 0.3 }}
          >
            <NavItem to="/contact" onClick={closeMenu}>Contact</NavItem>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: 0.35 }}
          >
            <NavItem to="/enquiry" onClick={closeMenu}>Enquiry</NavItem>
          </motion.div>
        </NavMenu>
      </NavContainer>
    </StyledHeader>
  )
}

export default Header