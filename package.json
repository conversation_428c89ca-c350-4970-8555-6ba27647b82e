{"name": "darvi-farm-website", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "node copy-content-to-public.js && vite", "build": "node check-directories.js && node copy-content-to-public.js && vite build && node copy-admin.js && node copy-content.js && node copy-images.js", "build:netlify": "node check-directories.js && node copy-content-to-public.js && node build.js", "test:build": "node check-directories.js && node copy-content-to-public.js && node test-build.js", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "setup-cms": "node setup-netlify-cms.js", "copy-content": "node copy-content-to-public.js"}, "dependencies": {"framer-motion": "^10.18.0", "leaflet": "^1.9.4", "netlify-identity-widget": "^1.9.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.5.3", "react-leaflet": "^4.2.1", "react-router-dom": "^6.20.0", "react-slick": "^0.29.0", "slick-carousel": "^1.8.1", "styled-components": "^6.1.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.2.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "vite": "^5.0.0"}}