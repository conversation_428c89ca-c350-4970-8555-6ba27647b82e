import { useState } from 'react'
import { motion } from 'framer-motion'
import styled from 'styled-components'
import { FaPhone, FaEnvelope, FaMapMarkerAlt, FaClock, FaUser, FaComment, FaPaperPlane } from 'react-icons/fa'
import SectionTitle from '../components/SectionTitle'
import CurvedDivider from '../components/CurvedDivider'

const ContactContainer = styled.div`
  padding-top: 100px;
  min-height: 100vh;
`

const HeroSection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(135deg, #f0f7eb 0%, #e8f5e3 100%);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%234a7c59" fill-opacity="0.1"/><circle cx="80" cy="80" r="2" fill="%234a7c59" fill-opacity="0.1"/></svg>');
    background-size: 100px 100px;
    pointer-events: none;
  }
`

const ContactSection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: white;
`

const ContactGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  
  @media (max-width: 968px) {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
`

const ContactInfo = styled(motion.div)`
  padding: 2rem;
`

const InfoTitle = styled.h2`
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  color: var(--primary-dark);
  font-family: var(--font-heading);
  font-weight: 700;
  
  @media (max-width: 768px) {
    font-size: 2rem;
  }
`

const InfoDescription = styled.p`
  font-size: 1.2rem;
  color: var(--text-muted);
  line-height: 1.7;
  margin-bottom: 3rem;
`

const ContactItem = styled(motion.div)`
  display: flex;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(74, 124, 89, 0.05);
  border-radius: 16px;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(74, 124, 89, 0.1);
    transform: translateX(8px);
  }
`

const ContactIcon = styled.div`
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.5rem;
  flex-shrink: 0;
  
  svg {
    color: white;
    font-size: 1.2rem;
  }
`

const ContactDetails = styled.div`
  flex: 1;
  
  h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-dark);
    font-weight: 600;
    font-size: 1.2rem;
  }
  
  p {
    color: var(--text-muted);
    margin: 0;
    line-height: 1.5;
  }
`

const ContactForm = styled(motion.form)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(74, 124, 89, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  
  @media (max-width: 768px) {
    padding: 2rem;
  }
`

const FormTitle = styled.h3`
  margin-bottom: 2rem;
  color: var(--primary-dark);
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1.8rem;
  text-align: center;
`

const FormGroup = styled.div`
  margin-bottom: 2rem;
  position: relative;
`

const FormLabel = styled.label`
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--primary-dark);
  font-size: 1rem;
`

const FormInput = styled.input`
  width: 100%;
  padding: 1rem 1.5rem;
  border: 2px solid rgba(74, 124, 89, 0.2);
  border-radius: 12px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 4px rgba(74, 124, 89, 0.1);
  }
  
  &::placeholder {
    color: var(--text-muted);
  }
`

const FormTextarea = styled.textarea`
  width: 100%;
  padding: 1rem 1.5rem;
  border: 2px solid rgba(74, 124, 89, 0.2);
  border-radius: 12px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  min-height: 120px;
  resize: vertical;
  font-family: inherit;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 4px rgba(74, 124, 89, 0.1);
  }
  
  &::placeholder {
    color: var(--text-muted);
  }
`

const SubmitButton = styled(motion.button)`
  width: 100%;
  padding: 1.25rem 2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(74, 124, 89, 0.3);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }
`

const MapSection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(to bottom, white, #f8f9fa);
`

const MapContainer = styled.div`
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(74, 124, 89, 0.15);
  height: 400px;
  
  iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
`

const contactInfo = [
  {
    icon: <FaPhone />,
    title: "Phone Number",
    details: "+91 99868 90777\nMon-Sat 9:00 AM - 6:00 PM"
  },
  {
    icon: <FaEnvelope />,
    title: "Email Address",
    details: "<EMAIL>\<EMAIL>"
  },
  {
    icon: <FaMapMarkerAlt />,
    title: "Office Address",
    details: "#2 Totad building, Arjun Vihar\nGokul road Hubli, Karnataka 580030"
  },
  {
    icon: <FaClock />,
    title: "Business Hours",
    details: "Monday - Saturday: 9:00 AM - 6:00 PM\nSunday: Closed"
  }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
}

function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    alert('Thank you for your message! We will get back to you soon.')
    setFormData({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    })
    setIsSubmitting(false)
  }

  return (
    <ContactContainer>
      <HeroSection
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className="container">
          <SectionTitle
            subtitle="Get In Touch"
            title="Contact Darvi Farmlands"
            description="Ready to start your farmland investment journey? Get in touch with our expert team for personalized consultation and investment guidance."
          />
        </div>
      </HeroSection>
      
      <CurvedDivider type="wave1" fillColor="white" opacity={1} />
      
      <ContactSection
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="container">
          <ContactGrid>
            <ContactInfo
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              <InfoTitle>Let's Start a Conversation</InfoTitle>
              <InfoDescription>
                We're here to help you make informed decisions about farmland investments. 
                Reach out to us for expert guidance, site visits, or any questions about our services.
              </InfoDescription>
              
              {contactInfo.map((item, index) => (
                <ContactItem
                  key={index}
                  variants={itemVariants}
                  whileHover={{ scale: 1.02 }}
                >
                  <ContactIcon>
                    {item.icon}
                  </ContactIcon>
                  <ContactDetails>
                    <h4>{item.title}</h4>
                    <p>{item.details}</p>
                  </ContactDetails>
                </ContactItem>
              ))}
            </ContactInfo>
            
            <ContactForm
              onSubmit={handleSubmit}
              variants={itemVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              <FormTitle>Send us a Message</FormTitle>
              
              <FormGroup>
                <FormLabel htmlFor="name">
                  <FaUser style={{ marginRight: '0.5rem', color: 'var(--primary-color)' }} />
                  Full Name
                </FormLabel>
                <FormInput
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter your full name"
                  required
                />
              </FormGroup>
              
              <FormGroup>
                <FormLabel htmlFor="email">
                  <FaEnvelope style={{ marginRight: '0.5rem', color: 'var(--primary-color)' }} />
                  Email Address
                </FormLabel>
                <FormInput
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="Enter your email address"
                  required
                />
              </FormGroup>
              
              <FormGroup>
                <FormLabel htmlFor="phone">
                  <FaPhone style={{ marginRight: '0.5rem', color: 'var(--primary-color)' }} />
                  Phone Number
                </FormLabel>
                <FormInput
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  placeholder="Enter your phone number"
                  required
                />
              </FormGroup>
              
              <FormGroup>
                <FormLabel htmlFor="subject">
                  <FaComment style={{ marginRight: '0.5rem', color: 'var(--primary-color)' }} />
                  Subject
                </FormLabel>
                <FormInput
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  placeholder="What would you like to discuss?"
                  required
                />
              </FormGroup>
              
              <FormGroup>
                <FormLabel htmlFor="message">Message</FormLabel>
                <FormTextarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  placeholder="Tell us more about your investment goals and requirements..."
                  required
                />
              </FormGroup>
              
              <SubmitButton
                type="submit"
                disabled={isSubmitting}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {isSubmitting ? 'Sending...' : 'Send Message'}
                <FaPaperPlane />
              </SubmitButton>
            </ContactForm>
          </ContactGrid>
        </div>
      </ContactSection>
      
      <CurvedDivider type="wave2" fillColor="#f8f9fa" opacity={1} />
      
      <MapSection
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="container">
          <SectionTitle
            subtitle="Visit Us"
            title="Find Our Office"
            description="Located in the heart of Hubli, our office is easily accessible for consultations and meetings."
          />
          
          <MapContainer>
            <iframe
              src="https://maps.google.com/maps?width=100%25&height=400&hl=en&q=Hubli,%20Karnataka,%20India&t=&z=14&ie=UTF8&iwloc=B&output=embed"
              width="100%"
              height="400"
              allowFullScreen={true}
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
              title="Darvi Farmlands Office Location"
            />
          </MapContainer>
        </div>
      </MapSection>
      
      <CurvedDivider type="wave3" fillColor="var(--primary-color)" opacity={0.1} />
    </ContactContainer>
  )
}

export default Contact