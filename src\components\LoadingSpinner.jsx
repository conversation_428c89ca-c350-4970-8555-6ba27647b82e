import React from 'react';
import styled from 'styled-components';

const SpinnerContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1.5rem;
  color: var(--primary-color, #4caf50);
`;

const SpinnerText = styled.div`
  margin-bottom: 1rem;
  text-align: center;
`;

const Spinner = styled.div`
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid var(--primary-color, #4caf50);
  border-radius: 50%;
  margin: 0 auto;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

/**
 * Loading spinner component
 * @param {Object} props - Component props
 * @param {string} [props.text="Loading content..."] - Text to display
 * @returns {JSX.Element} - Loading spinner component
 */
function LoadingSpinner({ text = "Loading content..." }) {
  return (
    <SpinnerContainer>
      <div>
        <SpinnerText>{text}</SpinnerText>
        <Spinner />
      </div>
    </SpinnerContainer>
  );
}

export default LoadingSpinner;
