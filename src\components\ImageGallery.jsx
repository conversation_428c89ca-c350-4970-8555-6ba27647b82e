import { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import ImageUpload from './ImageUpload';

const GalleryContainer = styled.div`
  margin-bottom: 2rem;
`;

const GalleryTitle = styled.h3`
  margin-bottom: 1rem;
  color: var(--primary-dark);
`;

const GalleryGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const GalleryItem = styled(motion.div)`
  position: relative;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  aspect-ratio: 1;
`;

const GalleryImage = styled.div`
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
`;

const RemoveButton = styled.button`
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: rgba(255, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  font-size: 14px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: rgba(255, 0, 0, 0.9);
  }
`;

const AddButton = styled.button`
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: var(--primary-dark);
  }
`;

/**
 * Image gallery component with add/remove functionality
 */
function ImageGallery({ title, images = [], onChange, maxImages = 10 }) {
  const [showUpload, setShowUpload] = useState(false);

  const handleAddImage = (imageUrl) => {
    if (imageUrl) {
      const newImages = [...images, imageUrl];
      onChange(newImages);
      setShowUpload(false);
    }
  };

  const handleRemoveImage = (index) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    onChange(newImages);
  };

  return (
    <GalleryContainer>
      <GalleryTitle>{title}</GalleryTitle>
      
      <GalleryGrid>
        {images.map((image, index) => (
          <GalleryItem
            key={index}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <GalleryImage style={{ backgroundImage: `url(${image})` }} />
            <RemoveButton onClick={() => handleRemoveImage(index)}>×</RemoveButton>
          </GalleryItem>
        ))}
      </GalleryGrid>
      
      {showUpload ? (
        <ImageUpload
          label="Add new image"
          onChange={handleAddImage}
          value=""
        />
      ) : (
        <AddButton 
          onClick={() => setShowUpload(true)}
          disabled={images.length >= maxImages}
        >
          {images.length >= maxImages 
            ? `Maximum of ${maxImages} images reached` 
            : 'Add Image'}
        </AddButton>
      )}
    </GalleryContainer>
  );
}

export default ImageGallery;
