import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import styled from 'styled-components'

const HeroContainer = styled.section`
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1920&q=80');
  background-size: cover;
  background-position: center;
  color: var(--text-light);

  @media (max-width: 768px) {
    min-height: 500px;
    height: 70vh;
  }

  @media (max-width: 480px) {
    min-height: 450px;
    height: 60vh;
  }
`

const HeroContent = styled.div`
  max-width: 800px;
  padding: 0 2rem;
  margin-top: 4rem;

  @media (max-width: 768px) {
    padding: 0 1rem;
  }
`

const HeroTitle = styled(motion.h1)`
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  color: var(--text-light);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  @media (max-width: 480px) {
    font-size: 2rem;
    margin-bottom: 1rem;
  }
`

const HeroSubtitle = styled(motion.p)`
  font-size: 1.25rem;
  margin-bottom: 2rem;
  max-width: 600px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.5;

  @media (max-width: 768px) {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
  }

  @media (max-width: 480px) {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }
`

const ButtonGroup = styled(motion.div)`
  display: flex;
  gap: 1rem;

  @media (max-width: 576px) {
    flex-direction: column;
    gap: 0.75rem;
  }
`

function HeroSection({ title, subtitle, primaryBtnText, primaryBtnLink, secondaryBtnText, secondaryBtnLink, backgroundImage }) {
  return (
    <HeroContainer style={backgroundImage ? { backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(${backgroundImage})` } : {}}>
      <div className="container">
        <HeroContent>
          <HeroTitle
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {title}
          </HeroTitle>

          <HeroSubtitle
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            {subtitle}
          </HeroSubtitle>

          <ButtonGroup
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {primaryBtnText && (
              <Link to={primaryBtnLink} className="btn btn-primary">
                {primaryBtnText}
              </Link>
            )}

            {secondaryBtnText && (
              <Link to={secondaryBtnLink} className="btn btn-outline">
                {secondaryBtnText}
              </Link>
            )}
          </ButtonGroup>
        </HeroContent>
      </div>
    </HeroContainer>
  )
}

export default HeroSection
