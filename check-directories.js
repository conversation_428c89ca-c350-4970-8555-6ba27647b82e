/**
 * Simple script to check if all required directories exist
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Required directories
const requiredDirs = [
  path.join(__dirname, 'public'),
  path.join(__dirname, 'public', 'admin'),
  path.join(__dirname, 'public', 'images'),
  path.join(__dirname, 'public', 'images', 'uploads'),
  path.join(__dirname, 'src'),
  path.join(__dirname, 'src', 'content')
];

// Check if each directory exists and create it if it doesn't
requiredDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`Created directory: ${dir}`);
    
    // Create a placeholder file
    const placeholderPath = path.join(dir, 'placeholder.txt');
    fs.writeFileSync(placeholderPath, `This is a placeholder file to ensure the ${path.basename(dir)} directory exists.`);
    console.log(`Created placeholder file: ${placeholderPath}`);
  } else {
    console.log(`Directory exists: ${dir}`);
  }
});

console.log('All required directories exist or have been created.');
