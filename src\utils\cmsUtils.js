/**
 * Utility functions for fetching and managing CMS content
 */

import { getImageUrl, getImageWithFallback } from './imageUtils';

/**
 * Fetch content for a specific page from the CMS
 * @param {string} pageName - The name of the page to fetch content for
 * @returns {Promise<Object>} - The page content with processed image URLs
 */
export const fetchPageContent = async (pageName) => {
  try {
    // Try multiple possible locations for content files
    const possiblePaths = [
      `/content/${pageName}.json`,
      `/src/content/${pageName}.json`,
      `/public/content/${pageName}.json`,
      `/${pageName}.json`
    ];

    let response = null;
    let succeeded = false;

    // Try each path until one works
    for (const path of possiblePaths) {
      try {
        console.log(`Trying to fetch content from: ${path}`);
        response = await fetch(path);

        if (response.ok) {
          console.log(`Successfully fetched content from: ${path}`);
          succeeded = true;
          break;
        }
      } catch (pathError) {
        console.log(`Failed to fetch from ${path}: ${pathError.message}`);
      }
    }

    // If all paths fail, throw an error
    if (!succeeded || !response) {
      throw new Error(`Failed to fetch ${pageName} content from all possible paths`);
    }

    const data = await response.json();
    return processContentImages(data);
  } catch (error) {
    console.error(`Error fetching ${pageName} content:`, error);

    // Return default content as a last resort
    return getDefaultContent(pageName);
  }
};

/**
 * Get default content for a page if the content file can't be loaded
 * @param {string} pageName - The name of the page
 * @returns {Object} - Default content for the page
 */
const getDefaultContent = (pageName) => {
  // Basic default content for each page
  const defaults = {
    home: {
      heroTitle: "Premium Nursery Plants & Agricultural Solutions",
      heroSubtitle: "Darvi Group is your trusted partner for high-quality nursery plants and innovative agricultural solutions since 2018.",
      primaryBtnText: "Explore Our Products",
      primaryBtnLink: "/farms",
      secondaryBtnText: "Contact Us",
      secondaryBtnLink: "/contact",
      farmProjectsSection: {
        subtitle: "Our Farm Projects",
        title: "Managed Farm Projects",
        description: "Explore our diverse portfolio of managed, completed, and upcoming agricultural projects."
      },
      featuredProjects: [],
      servicesSection: {
        subtitle: "What We Offer",
        title: "Our Services",
        description: "We provide comprehensive agricultural solutions from premium nursery plants to smart farming technologies."
      },
      services: [],
      aboutSection: {
        title: "About Darvi Group",
        text: "Darvi Group is a B2B and B2C provider of premium quality nursery plants and agricultural solutions.",
        buttonText: "Learn More About Us",
        buttonLink: "/about"
      },
      stats: [],
      ctaSection: {
        title: "Ready to Start Your Agricultural Journey?",
        text: "Contact us today to discuss your agricultural needs.",
        buttonText: "Contact Us Now",
        buttonLink: "/contact"
      }
    },
    about: {
      heroTitle: "About Darvi Group",
      heroSubtitle: "Your trusted partner in premium nursery plants and agricultural solutions since 2018.",
      primaryBtnText: "Meet Our Team",
      primaryBtnLink: "#team",
      storySection: {
        subtitle: "Our Story",
        title: "Company History and Mission",
        description: "Darvi Group was founded in 2018 as a provider of premium nursery plants and agricultural solutions."
      },
      contentBlocks: [],
      teamSection: {
        subtitle: "Our Team",
        title: "Meet the Experts",
        description: "Our diverse team brings together expertise in agriculture, technology, and finance."
      },
      teamMembers: [],
      valuesSection: {
        subtitle: "Our Values",
        title: "What Drives Us",
        description: "Our core values guide every aspect of our business."
      },
      values: []
    }
  };

  // Return default content for the requested page, or a simple object if no default exists
  return defaults[pageName] || {
    heroTitle: `${pageName.charAt(0).toUpperCase() + pageName.slice(1)} Page`,
    heroSubtitle: "Content is being updated. Please check back later."
  };
};

/**
 * Get the appropriate icon component based on icon name
 * @param {string} iconName - The name of the icon (e.g., "FaLeaf")
 * @returns {Function|null} - The icon component or null if not found
 */
export const getIconComponent = (iconName) => {
  // This is a simple implementation that assumes all icons are from react-icons/fa
  // You can expand this to support other icon libraries as needed
  if (!iconName) return null;

  try {
    // Dynamically import the icon from react-icons/fa
    const iconModule = require('react-icons/fa');

    // Map of available agricultural icons
    const availableIcons = {
      'FaLeaf': iconModule.FaLeaf,
      'FaMicrochip': iconModule.FaMicrochip,
      'FaSeedling': iconModule.FaSeedling,
      'FaChartLine': iconModule.FaChartLine,
      'FaTractor': iconModule.FaTractor,
      'FaUsers': iconModule.FaUsers,
      'FaRecycle': iconModule.FaRecycle,
      'FaAward': iconModule.FaAward,
      'FaTree': iconModule.FaTree,
      'FaWater': iconModule.FaWater,
      'FaIndustry': iconModule.FaIndustry,
      'FaCog': iconModule.FaCog,
      'FaLightbulb': iconModule.FaLightbulb,
      'FaHandshake': iconModule.FaHandshake,
      'FaShieldAlt': iconModule.FaShieldAlt,
      'FaGlobe': iconModule.FaGlobe,
      'FaSprout': iconModule.FaSprout || iconModule.FaSeedling // Use FaSprout if available, fallback to FaSeedling
    };

    return availableIcons[iconName] || iconModule[iconName] || null;
  } catch (error) {
    console.error(`Error loading icon ${iconName}:`, error);
    return null;
  }
};

/**
 * Format a date string
 * @param {string} dateString - ISO date string
 * @returns {string} - Formatted date string
 */
export const formatDate = (dateString) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

/**
 * Convert markdown to HTML (simple implementation)
 * @param {string} markdown - Markdown text
 * @returns {string} - HTML string
 */
export const markdownToHtml = (markdown) => {
  if (!markdown) return '';

  // This is a very simple implementation
  // For a real application, use a library like marked or remark
  return markdown
    .replace(/## (.*)/g, '<h2>$1</h2>')
    .replace(/# (.*)/g, '<h1>$1</h1>')
    .replace(/\*\*(.*)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*)\*/g, '<em>$1</em>')
    .replace(/\n- (.*)/g, '<ul><li>$1</li></ul>')
    .replace(/\n/g, '<br />');
};

/**
 * Process image URLs in content data
 * @param {Object} data - The content data object
 * @returns {Object} - The processed content data
 */
export const processContentImages = (data) => {
  if (!data) return data;

  // Create a deep copy of the data
  const processedData = JSON.parse(JSON.stringify(data));

  // Process the data recursively
  const processObject = (obj) => {
    if (!obj || typeof obj !== 'object') return;

    Object.keys(obj).forEach(key => {
      // If the key contains 'image' or 'Image', process it as an image URL
      if (
        (key.toLowerCase().includes('image') || key.toLowerCase().includes('img')) &&
        typeof obj[key] === 'string'
      ) {
        obj[key] = getImageUrl(obj[key]);
      }
      // If it's an array, process each item
      else if (Array.isArray(obj[key])) {
        obj[key].forEach(item => {
          if (typeof item === 'object') {
            processObject(item);
          }
        });
      }
      // If it's an object, process it recursively
      else if (typeof obj[key] === 'object' && obj[key] !== null) {
        processObject(obj[key]);
      }
    });
  };

  processObject(processedData);
  return processedData;
};
