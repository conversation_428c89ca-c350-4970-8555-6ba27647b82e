import { useState } from 'react'
import { motion } from 'framer-motion'
import styled from 'styled-components'
import { FaPlay, FaPause, FaVolumeUp, FaExpand } from 'react-icons/fa'
import SectionTitle from './SectionTitle'

const VideoSection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 80 80"><circle cx="40" cy="40" r="2" fill="%234a7c59" fill-opacity="0.08"/></svg>');
    background-size: 80px 80px;
    pointer-events: none;
  }
`

const VideoGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  position: relative;
  z-index: 1;
  
  @media (max-width: 768px) {
    gap: 2rem;
  }
`

const VideoCard = styled(motion.div)`
  position: relative;
  border-radius: 24px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 40px rgba(74, 124, 89, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s ease;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 30px 60px rgba(74, 124, 89, 0.2);
  }
  
  @media (max-width: 768px) {
    &:hover {
      transform: translateY(-4px);
    }
  }
`

const VideoContainer = styled.div`
  position: relative;
  width: 100%;
  height: 500px;
  background: #000;
  overflow: hidden;
  
  @media (max-width: 768px) {
    height: 300px;
  }
`

const VideoPlaceholder = styled.div`
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.3), rgba(74, 124, 89, 0.3));
  }
`

const PlayButton = styled(motion.button)`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 3px solid rgba(74, 124, 89, 0.3);
  color: var(--primary-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  transition: all 0.3s ease;
  z-index: 2;
  
  &:hover {
    background: var(--primary-color);
    color: white;
    transform: translate(-50%, -50%) scale(1.1);
    border-color: var(--primary-color);
  }
  
  &:before {
    content: '';
    position: absolute;
    inset: -8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    animation: pulse 2s ease-in-out infinite;
  }
  
  @keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.2); opacity: 0.3; }
  }
  
  @media (max-width: 768px) {
    width: 60px;
    height: 60px;
    font-size: 1.4rem;
  }
`

const VideoControls = styled(motion.div)`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.8));
  padding: 2rem 1.5rem 1.5rem;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  
  ${VideoCard}:hover & {
    transform: translateY(0);
  }
`

const ControlsRow = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  color: white;
`

const ControlButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
  }
`

const ProgressBar = styled.div`
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  
  &::after {
    content: '';
    display: block;
    height: 100%;
    width: 30%;
    background: var(--accent-color);
    border-radius: 2px;
  }
`

const VideoContent = styled.div`
  padding: 2rem;
  
  @media (max-width: 768px) {
    padding: 1.5rem;
  }
`

const VideoTitle = styled.h3`
  margin-bottom: 1rem;
  color: var(--primary-dark);
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1.5rem;
  
  @media (max-width: 768px) {
    font-size: 1.3rem;
  }
`

const VideoDescription = styled.p`
  color: var(--text-muted);
  line-height: 1.6;
  font-size: 1rem;
  margin-bottom: 1.5rem;
`

const VideoStats = styled.div`
  display: flex;
  gap: 2rem;
  
  @media (max-width: 768px) {
    gap: 1rem;
  }
`

const StatItem = styled.div`
  text-align: center;
  
  .number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    font-family: var(--font-heading);
  }
  
  .label {
    font-size: 0.9rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  @media (max-width: 768px) {
    .number {
      font-size: 1.2rem;
    }
    
    .label {
      font-size: 0.8rem;
    }
  }
`

const videos = [
  {
    id: 1,
    title: "Modern Farming Techniques",
    description: "Discover how we use cutting-edge technology and sustainable practices to maximize crop yields while preserving the environment for future generations.",
    thumbnail: "https://images.unsplash.com/photo-1558906307-1a1c52b5ac8a?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwyfHxmYXJtaW5nJTIwdHJhY3RvciUyMGFncmljdWx0dXJlJTIwZ3JlZW4lMjBmaWVsZHN8ZW58MHwwfHxncmVlbnwxNzU0NDE3NzYxfDA&ixlib=rb-4.1.0&q=85",
    duration: "3:45",
    views: "12.5K",
    category: "Technology"
  },
  {
    id: 2,
    title: "Aerial View of Our Farmlands",
    description: "Take a bird's eye view of our expansive farmlands and see the organized irrigation systems and crop management that ensures optimal growth conditions.",
    thumbnail: "https://images.unsplash.com/photo-1655672295160-a16253a8a471?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwzfHxkcm9uZSUyMGZvb3RhZ2UlMjBmYXJtbGFuZCUyMGlycmlnYXRpb24lMjBhZXJpYWwlMjB2aWV3fGVufDB8MHx8Z3JlZW58MTc1NDQxNzc2MXww&ixlib=rb-4.1.0&q=85",
    duration: "5:20",
    views: "8.9K",
    category: "Overview"
  }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.3,
      delayChildren: 0.2
    }
  }
}

const cardVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut"
    }
  }
}

function VideoSectionComponent() {
  const [playingVideo, setPlayingVideo] = useState(null)

  const handlePlayVideo = (videoId) => {
    setPlayingVideo(videoId)
    // In a real implementation, you would start video playback here
  }

  return (
    <VideoSection
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
    >
      <div className="container">
        <SectionTitle
          subtitle="Video Gallery"
          title="See Our Farmlands in Action"
          description="Watch our professional farming operations and get an inside look at how we manage sustainable agriculture for maximum returns."
        />
        
        <VideoGrid
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
        >
          {videos.map((video, index) => (
            <VideoCard
              key={video.id}
              variants={cardVariants}
              whileHover={{ scale: 1.02 }}
            >
              <VideoContainer>
                <VideoPlaceholder
                  style={{ backgroundImage: `url(${video.thumbnail})` }}
                >
                  <PlayButton
                    onClick={() => handlePlayVideo(video.id)}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <FaPlay style={{ marginLeft: '4px' }} />
                  </PlayButton>
                </VideoPlaceholder>
                
                <VideoControls>
                  <ControlsRow>
                    <ControlButton>
                      <FaPlay />
                    </ControlButton>
                    <ProgressBar />
                    <ControlButton>
                      <FaVolumeUp />
                    </ControlButton>
                    <ControlButton>
                      <FaExpand />
                    </ControlButton>
                  </ControlsRow>
                </VideoControls>
              </VideoContainer>
              
              <VideoContent>
                <VideoTitle>{video.title}</VideoTitle>
                <VideoDescription>{video.description}</VideoDescription>
                
                <VideoStats>
                  <StatItem>
                    <div className="number">{video.duration}</div>
                    <div className="label">Duration</div>
                  </StatItem>
                  <StatItem>
                    <div className="number">{video.views}</div>
                    <div className="label">Views</div>
                  </StatItem>
                  <StatItem>
                    <div className="number">{video.category}</div>
                    <div className="label">Category</div>
                  </StatItem>
                </VideoStats>
              </VideoContent>
            </VideoCard>
          ))}
        </VideoGrid>
      </div>
    </VideoSection>
  )
}

export default VideoSectionComponent