import React from 'react'
import styled from 'styled-components'

const IconWrapper = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: ${props => props.size || '24px'};
  height: ${props => props.size || '24px'};
`

const DarviSVG = styled.svg`
  width: 100%;
  height: 100%;
  fill: currentColor;
`

function DarviIcon({ size = "24px", color = "currentColor", className = "" }) {
  return (
    <IconWrapper size={size} className={className}>
      <DarviSVG
        viewBox="0 0 100 100"
        xmlns="http://www.w3.org/2000/svg"
        style={{ color }}
      >
        {/* Main leaf shape - based on the provided green leaf design */}
        <defs>
          <linearGradient id="leafGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#7fb069" />
            <stop offset="50%" stopColor="#4a7c59" />
            <stop offset="100%" stopColor="#2d5016" />
          </linearGradient>
        </defs>
        
        {/* Outer leaf shape */}
        <path
          d="M50 5 C75 5, 95 25, 95 50 C95 75, 75 95, 50 95 C25 95, 5 75, 5 50 C5 25, 25 5, 50 5 Z"
          fill="url(#leafGradient)"
        />
        
        {/* Inner leaf detail - creating the characteristic leaf vein */}
        <path
          d="M50 15 C65 15, 80 30, 80 50 C80 70, 65 85, 50 85 C35 85, 20 70, 20 50 C20 30, 35 15, 50 15 Z"
          fill="none"
          stroke="#ffffff"
          strokeWidth="1"
          opacity="0.3"
        />
        
        {/* Central vein */}
        <path
          d="M50 15 L50 85"
          stroke="#ffffff"
          strokeWidth="2"
          opacity="0.4"
        />
        
        {/* Side veins */}
        <path
          d="M50 25 L35 35 M50 35 L35 45 M50 45 L35 55 M50 55 L35 65 M50 65 L35 75"
          stroke="#ffffff"
          strokeWidth="1"
          opacity="0.3"
        />
        
        <path
          d="M50 25 L65 35 M50 35 L65 45 M50 45 L65 55 M50 55 L65 65 M50 65 L65 75"
          stroke="#ffffff"
          strokeWidth="1"
          opacity="0.3"
        />
      </DarviSVG>
    </IconWrapper>
  )
}

export default DarviIcon
