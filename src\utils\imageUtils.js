/**
 * Utility functions for handling images in the CMS
 */

/**
 * Get the full URL for an image path
 * @param {string} imagePath - The path to the image
 * @returns {string} - The full URL to the image
 */
export const getImageUrl = (imagePath) => {
  if (!imagePath) return '';
  
  // If the path is already a full URL, return it as is
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }
  
  // If the path is a relative path, prepend the base URL
  const baseUrl = window.location.origin;
  
  // Ensure the path starts with a slash
  const normalizedPath = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;
  
  return `${baseUrl}${normalizedPath}`;
};

/**
 * Get a placeholder image URL if the provided image is not available
 * @param {string} imagePath - The path to the image
 * @param {string} category - The category of the image (e.g., 'hero', 'project', 'team')
 * @returns {string} - The image URL or a placeholder
 */
export const getImageWithFallback = (imagePath, category = 'general') => {
  if (imagePath) return getImageUrl(imagePath);
  
  // Return a placeholder based on the category
  const placeholders = {
    hero: 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1920&q=80',
    project: 'https://images.unsplash.com/photo-1464226184884-fa280b87c399?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
    team: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
    general: 'https://images.unsplash.com/photo-1625246333195-78d9c38ad449?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80'
  };
  
  return placeholders[category] || placeholders.general;
};

/**
 * Generate a responsive image srcset
 * @param {string} imagePath - The path to the image
 * @param {Array<number>} sizes - Array of image widths to include in the srcset
 * @returns {string} - The srcset attribute value
 */
export const generateSrcSet = (imagePath, sizes = [320, 640, 960, 1280, 1920]) => {
  if (!imagePath) return '';
  
  // This is a simplified implementation that assumes you have different sized versions of the image
  // In a real implementation, you might use a service like Cloudinary or Imgix to generate these on-the-fly
  
  const baseUrl = getImageUrl(imagePath);
  const extension = baseUrl.split('.').pop();
  const basePath = baseUrl.substring(0, baseUrl.lastIndexOf('.'));
  
  return sizes
    .map(size => `${basePath}-${size}.${extension} ${size}w`)
    .join(', ');
};

/**
 * Check if an image exists
 * @param {string} url - The URL of the image to check
 * @returns {Promise<boolean>} - Promise that resolves to true if the image exists
 */
export const imageExists = async (url) => {
  if (!url) return false;
  
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error('Error checking if image exists:', error);
    return false;
  }
};
