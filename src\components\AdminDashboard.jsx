import { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';

const DashboardContainer = styled.div`
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: 1.5rem;
  }

  @media (max-width: 480px) {
    padding: 1rem;
  }
`;

const DashboardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
`;

const DashboardTitle = styled.h1`
  color: var(--primary-dark);
`;

const AdminButton = styled.button`
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: var(--primary-dark);
  }
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  @media (max-width: 480px) {
    gap: 0.75rem;
  }
`;

const ContentCard = styled.div`
  background-color: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
`;

const CardHeader = styled.div`
  background-color: var(--primary-color);
  color: white;
  padding: 1rem;
`;

const CardTitle = styled.h3`
  margin: 0;
`;

const CardContent = styled.div`
  padding: 1rem;
`;

const CardActions = styled.div`
  display: flex;
  justify-content: flex-end;
  padding: 1rem;
  border-top: 1px solid #eee;
`;

const CardButton = styled(Link)`
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  font-size: 0.875rem;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: var(--primary-dark);
  }
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;

  @media (max-width: 992px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
`;

const StatCard = styled.div`
  background-color: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  text-align: center;
`;

const StatNumber = styled.div`
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  color: var(--text-dark);
  font-weight: 500;
`;

/**
 * Admin dashboard component for the CMS
 */
function AdminDashboard() {
  const [stats, setStats] = useState({
    pages: 0,
    images: 0,
    drafts: 0,
    published: 0
  });

  useEffect(() => {
    // In a real implementation, you would fetch this data from your CMS API
    // This is just a placeholder
    setStats({
      pages: 9,
      images: 24,
      drafts: 3,
      published: 6
    });
  }, []);

  const contentPages = [
    { id: 'home', title: 'Home Page', description: 'Main landing page content' },
    { id: 'about', title: 'About Page', description: 'Company information and team' },
    { id: 'approach', title: 'Approach Page', description: 'Farming philosophy and methods' },
    { id: 'farms', title: 'Farms Page', description: 'Farm locations and projects' },
    { id: 'investment', title: 'Investment Page', description: 'Investment opportunities' },
    { id: 'technology', title: 'Technology Page', description: 'Agricultural technology' },
    { id: 'sustainability', title: 'Sustainability Page', description: 'Sustainability initiatives' },
    { id: 'resources', title: 'Resources Page', description: 'Educational resources' },
    { id: 'contact', title: 'Contact Page', description: 'Contact information' }
  ];

  return (
    <DashboardContainer>
      <DashboardHeader>
        <DashboardTitle>Darvi Group CMS Dashboard</DashboardTitle>
        <AdminButton onClick={() => window.location.href = '/admin'}>
          Go to Netlify CMS
        </AdminButton>
      </DashboardHeader>

      <StatsContainer>
        <StatCard>
          <StatNumber>{stats.pages}</StatNumber>
          <StatLabel>Pages</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>{stats.images}</StatNumber>
          <StatLabel>Images</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>{stats.drafts}</StatNumber>
          <StatLabel>Draft Entries</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>{stats.published}</StatNumber>
          <StatLabel>Published Entries</StatLabel>
        </StatCard>
      </StatsContainer>

      <h2>Content Pages</h2>
      <ContentGrid>
        {contentPages.map(page => (
          <ContentCard key={page.id}>
            <CardHeader>
              <CardTitle>{page.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{page.description}</p>
            </CardContent>
            <CardActions>
              <CardButton to={`/admin/#/collections/${page.id}/entries/${page.id}`}>
                Edit Content
              </CardButton>
            </CardActions>
          </ContentCard>
        ))}
      </ContentGrid>
    </DashboardContainer>
  );
}

export default AdminDashboard;
