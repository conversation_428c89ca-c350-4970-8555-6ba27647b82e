import { motion } from 'framer-motion'
import styled from 'styled-components'
import { FaPlay, FaArrowRight, FaLeaf, FaChartLine } from 'react-icons/fa'

const HeroSection = styled(motion.section)`
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, #f0f7eb 0%, #e8f5e3 100%);
  overflow: hidden;
  padding-top: 80px;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%234a7c59" fill-opacity="0.1"/><circle cx="80" cy="80" r="2" fill="%234a7c59" fill-opacity="0.1"/><circle cx="50" cy="50" r="3" fill="%234a7c59" fill-opacity="0.1"/></svg>');
    background-size: 100px 100px;
    pointer-events: none;
  }
`

const HeroContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 1;
  
  @media (max-width: 968px) {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }
`

const HeroContent = styled(motion.div)`
  position: relative;
  
  @media (max-width: 968px) {
    order: 2;
  }
`

const HeroSubtitle = styled(motion.p)`
  color: var(--primary-color);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 1rem;
  font-size: 1rem;
  
  @media (max-width: 768px) {
    font-size: 0.9rem;
    letter-spacing: 1px;
  }
`

const HeroTitle = styled(motion.h1)`
  font-size: clamp(3rem, 6vw, 4.5rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: var(--primary-dark);
  font-family: var(--font-heading);
  
  .highlight {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
      border-radius: 2px;
    }
  }
`

const HeroDescription = styled(motion.p)`
  font-size: 1.3rem;
  color: var(--text-muted);
  line-height: 1.7;
  margin-bottom: 3rem;
  max-width: 500px;
  
  @media (max-width: 968px) {
    max-width: none;
  }
  
  @media (max-width: 768px) {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }
`

const HeroButtons = styled(motion.div)`
  display: flex;
  gap: 1.5rem;
  align-items: center;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
  
  @media (max-width: 968px) {
    justify-content: center;
  }
`

const PrimaryButton = styled(motion.button)`
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1.25rem 2.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(74, 124, 89, 0.3);
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(74, 124, 89, 0.4);
  }
  
  svg {
    transition: transform 0.3s ease;
  }
  
  &:hover svg {
    transform: translateX(4px);
  }
  
  @media (max-width: 768px) {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }
`

const SecondaryButton = styled(motion.button)`
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1.25rem 2rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  color: var(--primary-color);
  border: 2px solid rgba(74, 124, 89, 0.2);
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 1);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(74, 124, 89, 0.2);
  }
  
  @media (max-width: 768px) {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }
`

const HeroImageContainer = styled(motion.div)`
  position: relative;
  
  @media (max-width: 968px) {
    order: 1;
  }
`

const HeroImage = styled(motion.div)`
  position: relative;
  border-radius: 24px;
  overflow: hidden;
  height: 500px;
  background-size: cover;
  background-position: center;
  box-shadow: 0 25px 50px rgba(74, 124, 89, 0.2);
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(74, 124, 89, 0.1), rgba(244, 162, 97, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover::before {
    opacity: 1;
  }
  
  @media (max-width: 768px) {
    height: 400px;
  }
`

const FloatingCard = styled(motion.div)`
  position: absolute;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 15px 35px rgba(74, 124, 89, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  
  &.top-right {
    top: 20px;
    right: 20px;
    max-width: 200px;
  }
  
  &.bottom-left {
    bottom: 20px;
    left: 20px;
    max-width: 180px;
  }
  
  @media (max-width: 768px) {
    display: none;
  }
`

const CardIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.75rem;
  
  svg {
    color: white;
    font-size: 1rem;
  }
`

const CardTitle = styled.h4`
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--primary-dark);
  margin-bottom: 0.25rem;
`

const CardText = styled.p`
  font-size: 0.8rem;
  color: var(--text-muted);
  margin: 0;
  line-height: 1.4;
`

const FloatingElement = styled(motion.div)`
  position: absolute;
  z-index: 0;
  opacity: 0.6;
  pointer-events: none;
  color: var(--primary-color);
`

const heroVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut"
    }
  }
}

const imageVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 1,
      ease: "easeOut"
    }
  }
}

const floatingVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      delay: 1
    }
  }
}

function ModernHeroSection() {
  return (
    <HeroSection
      variants={heroVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Floating decorative elements */}
      <FloatingElement
        style={{ top: '20%', left: '10%' }}
        animate={{
          y: [0, -20, 0],
          rotate: [0, 5, 0]
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <FaLeaf size={50} />
      </FloatingElement>
      
      <FloatingElement
        style={{ top: '70%', right: '15%' }}
        animate={{
          y: [0, -15, 0],
          rotate: [0, -5, 0]
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
      >
        <FaChartLine size={40} />
      </FloatingElement>

      <div className="container">
        <HeroContainer>
          <HeroContent variants={itemVariants}>
            <HeroSubtitle variants={itemVariants}>
              Premium Farmland Investment
            </HeroSubtitle>
            
            <HeroTitle variants={itemVariants}>
              <span className="highlight">Managed Farmland</span><br />
              for Everyone
            </HeroTitle>
            
            <HeroDescription variants={itemVariants}>
              Invest in professionally managed farmland with guaranteed 12% annual returns. 
              Join hundreds of satisfied investors who trust our sustainable agricultural expertise.
            </HeroDescription>
            
            <HeroButtons variants={itemVariants}>
              <PrimaryButton
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Start Investing
                <FaArrowRight />
              </PrimaryButton>
              
              <SecondaryButton
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <FaPlay />
                Watch Demo
              </SecondaryButton>
            </HeroButtons>
          </HeroContent>
          
          <HeroImageContainer variants={imageVariants}>
            <HeroImage
              style={{ 
                backgroundImage: `url(https://images.unsplash.com/photo-1734261780213-765e29537e1f?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwyfHxidXNpbmVzcyUyMHRlYW0lMjBhZ3JpY3VsdHVyZSUyMHByb2Zlc3Npb25hbHMlMjBmYXJtbGFuZHxlbnwwfDB8fHwxNzU0NDE2NzE3fDA&ixlib=rb-4.1.0&q=85)` 
              }}
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.3 }}
            />
            
            <FloatingCard 
              className="top-right"
              variants={floatingVariants}
              animate={{
                y: [0, -10, 0]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <CardIcon>
                <FaChartLine />
              </CardIcon>
              <CardTitle>12% Returns</CardTitle>
              <CardText>Guaranteed annual returns on your investment</CardText>
            </FloatingCard>
            
            <FloatingCard 
              className="bottom-left"
              variants={floatingVariants}
              animate={{
                y: [0, -8, 0]
              }}
              transition={{
                duration: 5,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1
              }}
            >
              <CardIcon>
                <FaLeaf />
              </CardIcon>
              <CardTitle>Sustainable</CardTitle>
              <CardText>Eco-friendly farming practices</CardText>
            </FloatingCard>
          </HeroImageContainer>
        </HeroContainer>
      </div>
    </HeroSection>
  )
}

export default ModernHeroSection