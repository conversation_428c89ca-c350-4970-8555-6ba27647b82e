@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

:root {
  /* Enhanced Farmland Color Palette */
  --primary-color: #2d5016;
  --primary-light: #4a7c59;
  --primary-dark: #1a3009;
  --secondary-color: #8b4513;
  --secondary-light: #cd853f;
  --secondary-dark: #654321;
  --accent-color: #f4a261;
  --accent-light: #ffc107;
  --accent-dark: #e76f51;

  /* Farmland Theme Colors */
  --earth-brown: #8b4513;
  --rich-soil: #3e2723;
  --fertile-green: #2d5016;
  --crop-green: #4a7c59;
  --harvest-gold: #ffc107;
  --wheat-gold: #daa520;
  --sky-blue: #87ceeb;
  --sunset-orange: #ff8a65;
  --field-green: #7cb342;

  /* Text Colors */
  --text-light: #f5f5f5;
  --text-dark: #2e2e2e;
  --text-muted: #666666;

  /* Background Colors */
  --background-light: #fafafa;
  --background-cream: #f8f6f0;
  --background-dark: #333333;
  --background-overlay: rgba(0, 0, 0, 0.6);

  /* Status Colors */
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --info-color: #2196f3;

  /* Farmland Gradients */
  --gradient-farmland: linear-gradient(135deg, var(--fertile-green) 0%, var(--crop-green) 50%, var(--field-green) 100%);
  --gradient-harvest: linear-gradient(135deg, var(--harvest-gold) 0%, var(--wheat-gold) 100%);
  --gradient-earth: linear-gradient(135deg, var(--earth-brown) 0%, var(--rich-soil) 100%);
  --gradient-sky: linear-gradient(135deg, var(--sky-blue) 0%, var(--primary-light) 100%);

  /* Design System */
  --border-radius: 12px;
  --border-radius-sm: 6px;
  --border-radius-lg: 20px;

  /* Spacing Scale */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;
  --spacing-xxxl: 4rem;

  /* Typography */
  --font-heading: 'Playfair Display', serif;
  --font-body: 'Inter', 'Segoe UI', sans-serif;
  --font-accent: 'Poppins', sans-serif;

  /* Transitions */
  --transition-speed: 0.3s;
  --transition-smooth: cubic-bezier(0.4, 0, 0.2, 1);

  /* Shadows */
  --box-shadow: 0 4px 6px rgba(45, 80, 22, 0.1);
  --box-shadow-lg: 0 10px 25px rgba(45, 80, 22, 0.15);
  --box-shadow-xl: 0 20px 40px rgba(45, 80, 22, 0.2);
  --shadow-light: 0 2px 10px rgba(45, 80, 22, 0.1);
  --shadow-medium: 0 4px 20px rgba(45, 80, 22, 0.15);
  --shadow-heavy: 0 8px 30px rgba(45, 80, 22, 0.2);
  --shadow-farmland: 0 4px 20px rgba(45, 80, 22, 0.25);

  /* Agricultural Patterns */
  --pattern-dots: radial-gradient(circle, var(--primary-color) 1px, transparent 1px);
  --pattern-lines: repeating-linear-gradient(45deg, transparent, transparent 10px, var(--primary-color) 10px, var(--primary-color) 11px);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-body);
  line-height: 1.7;
  color: var(--text-dark);
  background-color: var(--background-cream);
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.3;
  margin-bottom: var(--spacing-md);
  color: var(--primary-dark);
  letter-spacing: -0.02em;
}

h1 {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 800;
  letter-spacing: -0.03em;
}

h2 {
  font-size: clamp(2rem, 4vw, 2.8rem);
  font-weight: 700;
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2.2rem);
  font-weight: 600;
}

h4 {
  font-size: clamp(1.25rem, 2.5vw, 1.8rem);
  font-weight: 600;
}

h5 {
  font-size: 1.25rem;
  font-weight: 500;
}

h6 {
  font-size: 1.1rem;
  font-weight: 500;
}

p {
  margin-bottom: var(--spacing-md);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-speed);
}

a:hover {
  color: var(--primary-dark);
}

img {
  max-width: 100%;
  height: auto;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }
}

.section {
  padding: var(--spacing-xxl) 0;
}

@media (max-width: 768px) {
  .section {
    padding: var(--spacing-xl) 0;
  }
}

@media (max-width: 480px) {
  .section {
    padding: var(--spacing-lg) 0;
  }
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.875rem 2rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 0.95rem;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-speed) var(--transition-smooth);
  border: none;
  font-family: var(--font-accent);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em;
  box-shadow: var(--box-shadow);
}

@media (max-width: 768px) {
  .btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.85rem;
    width: 100%;
    max-width: 280px;
  }
}

.btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover:before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: var(--text-light);
  border: 2px solid transparent;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  color: var(--text-light);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-lg);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
  color: var(--text-light);
  border: 2px solid transparent;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, var(--secondary-dark), var(--secondary-color));
  color: var(--text-light);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-lg);
}

.btn-outline {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  backdrop-filter: blur(10px);
}

.btn-outline:hover {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: var(--text-light);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-lg);
}

.btn-accent {
  background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
  color: var(--text-dark);
  border: 2px solid transparent;
}

.btn-accent:hover {
  background: linear-gradient(135deg, var(--accent-dark), var(--accent-color));
  color: var(--text-light);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-lg);
}

.text-center {
  text-align: center;
}

.mb-1 {
  margin-bottom: var(--spacing-sm);
}

.mb-2 {
  margin-bottom: var(--spacing-md);
}

.mb-3 {
  margin-bottom: var(--spacing-lg);
}

.mb-4 {
  margin-bottom: var(--spacing-xl);
}

.mb-5 {
  margin-bottom: var(--spacing-xxl);
}

.mt-1 {
  margin-top: var(--spacing-sm);
}

.mt-2 {
  margin-top: var(--spacing-md);
}

.mt-3 {
  margin-top: var(--spacing-lg);
}

.mt-4 {
  margin-top: var(--spacing-xl);
}

.mt-5 {
  margin-top: var(--spacing-xxl);
}

/* Agricultural Theme Components */
.agricultural-pattern {
  position: relative;
  overflow: hidden;
}

.agricultural-pattern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: var(--pattern-dots);
  background-size: 20px 20px;
  opacity: 0.05;
  pointer-events: none;
}

.section-divider {
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  margin: var(--spacing-xl) 0;
}

.card-agricultural {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--box-shadow-lg);
  border: 1px solid rgba(74, 124, 89, 0.1);
  transition: all var(--transition-speed) var(--transition-smooth);
  position: relative;
  overflow: hidden;
}

.card-agricultural::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.card-agricultural:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-xl);
}

.stats-counter {
  font-family: var(--font-heading);
  font-weight: 800;
  font-size: clamp(2rem, 4vw, 3rem);
  color: var(--primary-color);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.agricultural-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin-bottom: var(--spacing-md);
  box-shadow: var(--box-shadow);
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.parallax-section {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}

.overlay-dark {
  background: linear-gradient(135deg, var(--background-overlay), rgba(0, 0, 0, 0.4));
}

.overlay-green {
  background: linear-gradient(135deg, rgba(74, 124, 89, 0.8), rgba(123, 179, 105, 0.6));
}

/* Responsive Design */
@media (max-width: 768px) {
  .section {
    padding: var(--spacing-xl) 0;
  }

  .card-agricultural {
    padding: var(--spacing-lg);
  }

  .agricultural-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
}