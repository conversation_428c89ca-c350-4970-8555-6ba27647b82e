# Darvi Group Website

This is the official website for Darvi Group, a provider of premium nursery plants and agricultural solutions since 2018.

## Features

- Modern, responsive design
- Content Management System (CMS) for easy content updates
- Image upload and management
- Comprehensive content editing for all pages

## Technology Stack

- React.js
- Vite
- Styled Components
- Framer Motion
- React Router
- Decap CMS (formerly Netlify CMS)
- Netlify for hosting and deployment

## Getting Started

### Installation

Install the dependencies:

```bash
npm install
```

### Development

Start the development server with HMR:

```bash
npm run dev
```

Your application will be available at `http://localhost:5173`.

## Content Management

This website uses Decap CMS for content management. To access the CMS:

1. Deploy the site to Netlify
2. Set up Netlify Identity and Git Gateway
3. Navigate to `/admin` on your deployed site
4. Log in with your credentials

For detailed instructions, see the [CMS_INSTRUCTIONS.md](./CMS_INSTRUCTIONS.md) file.

## Building for Production

Create a production build:

```bash
npm run build
```

For Netlify deployment, use:

```bash
npm run build:netlify
```

## Deployment

This website is designed to be deployed on Netlify. For detailed deployment instructions, see the [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md) file.

## Project Structure

```
darvi-farm-website/
├── public/              # Static assets
│   ├── admin/           # CMS configuration
│   └── images/          # Images
├── src/                 # Source code
│   ├── components/      # React components
│   ├── content/         # CMS content (JSON)
│   ├── pages/           # Page components
│   ├── utils/           # Utility functions
│   ├── App.jsx          # Main application component
│   └── main.jsx         # Application entry point
├── .gitignore           # Git ignore file
├── package.json         # Project dependencies
├── vite.config.js       # Vite configuration
└── README.md            # Project documentation
```

## Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the project for production
- `npm run build:netlify` - Build the project for Netlify deployment
- `npm run preview` - Preview the production build locally
- `npm run setup-cms` - Set up the CMS

## Contact

For questions or support, please contact:

- Email: <EMAIL>
- Phone: +91 99868 90777
