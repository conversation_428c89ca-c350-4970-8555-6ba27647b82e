/**
 * Simple script to copy the images directory to the dist folder
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Source and destination directories
const sourceDir = path.join(__dirname, 'public', 'images');
const destDir = path.join(__dirname, 'dist', 'images');

// Create the source directory if it doesn't exist
if (!fs.existsSync(sourceDir)) {
  fs.mkdirSync(sourceDir, { recursive: true });
  console.log(`Created source directory: ${sourceDir}`);

  // Create a placeholder file
  fs.writeFileSync(path.join(sourceDir, 'placeholder.txt'), 'This is a placeholder file to ensure the images directory exists.');
  console.log(`Created placeholder file in ${sourceDir}`);

  // Create the uploads directory
  const uploadsDir = path.join(sourceDir, 'uploads');
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
    console.log(`Created uploads directory: ${uploadsDir}`);

    // Create a placeholder file
    fs.writeFileSync(path.join(uploadsDir, 'placeholder.txt'), 'This is a placeholder file to ensure the uploads directory exists.');
    console.log(`Created placeholder file in ${uploadsDir}`);
  }
}

// Create the destination directory if it doesn't exist
if (!fs.existsSync(destDir)) {
  fs.mkdirSync(destDir, { recursive: true });
  console.log(`Created directory: ${destDir}`);
}

// Copy all files from the source directory to the destination directory
const copyFiles = (src, dest) => {
  const files = fs.readdirSync(src);

  files.forEach(file => {
    const srcPath = path.join(src, file);
    const destPath = path.join(dest, file);

    const stat = fs.statSync(srcPath);

    if (stat.isDirectory()) {
      // If it's a directory, create it in the destination and copy its contents
      if (!fs.existsSync(destPath)) {
        fs.mkdirSync(destPath, { recursive: true });
      }
      copyFiles(srcPath, destPath);
    } else {
      // If it's a file, copy it to the destination
      fs.copyFileSync(srcPath, destPath);
      console.log(`Copied: ${srcPath} -> ${destPath}`);
    }
  });
};

try {
  // Check if the source directory exists
  if (fs.existsSync(sourceDir)) {
    copyFiles(sourceDir, destDir);
    console.log('Images directory copied successfully!');
  } else {
    console.log(`Source directory ${sourceDir} does not exist. Creating empty destination directory.`);

    // Create the uploads directory in the destination
    const uploadsDir = path.join(destDir, 'uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
      console.log(`Created directory: ${uploadsDir}`);
    }
  }
} catch (error) {
  console.error('Error copying images directory:', error);
  // Don't exit with error code, just log the error
  console.log('Continuing build process despite image copy error...');
}
