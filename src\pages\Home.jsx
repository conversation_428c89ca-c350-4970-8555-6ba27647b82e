import { motion } from 'framer-motion'
import styled from 'styled-components'
import ModernHeroSection from '../components/ModernHeroSection'
import InvestmentCalculator from '../components/InvestmentCalculator'
import WhyInvestSection from '../components/WhyInvestSection'
import PropertyShowcase from '../components/PropertyShowcase'
import InvestmentProcess from '../components/InvestmentProcess'
import InvestmentStats from '../components/InvestmentStats'
import FAQSection from '../components/FAQSection'
import AwardsSection from '../components/AwardsSection'
import VideoSection from '../components/VideoSection'
import CurvedDivider from '../components/CurvedDivider'

const HomeContainer = styled.div`
  overflow-x: hidden;
`

const SectionDivider = styled(motion.div)`
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  margin: 0;
  opacity: 0.3;
`

const ImageGallerySection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(to bottom, #f8f9fa, #ffffff);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 80 80"><circle cx="40" cy="40" r="2" fill="%234a7c59" fill-opacity="0.08"/></svg>');
    background-size: 80px 80px;
    pointer-events: none;
  }
`

const ImageGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
`

const ImageCard = styled(motion.div)`
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  height: 300px;
  background-size: cover;
  background-position: center;
  cursor: pointer;
  box-shadow: 0 15px 30px rgba(74, 124, 89, 0.15);
  transition: all 0.4s ease;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(74, 124, 89, 0.25);
  }
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.6) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover::before {
    opacity: 1;
  }
  
  @media (max-width: 768px) {
    height: 250px;
  }
`

const ImageOverlay = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 2rem;
  color: white;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  
  ${ImageCard}:hover & {
    transform: translateY(0);
  }
  
  h4 {
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
    font-weight: 600;
    font-family: var(--font-heading);
  }
  
  p {
    font-size: 0.95rem;
    opacity: 0.9;
    margin: 0;
  }
`

const galleryImages = [
  {
    src: "/darvi-images/field1.png",
    title: "Mango Orchard Development",
    description: "Premium mango cultivation with modern irrigation systems"
  },
  {
    src: "/darvi-images/field2.jpg",
    title: "Organic Vegetable Farming",
    description: "Sustainable organic farming practices for healthy produce"
  },
  {
    src: "/darvi-images/field3.jpg",
    title: "Smart Agriculture Technology",
    description: "IoT-enabled farming with precision agriculture techniques"
  },
  {
    src: "/darvi-images/field4.jpg",
    title: "Rice Cultivation Fields",
    description: "Traditional rice farming with modern water management"
  },
  {
    src: "/darvi-images/field5.jpg",
    title: "Medicinal Plants Garden",
    description: "High-value medicinal plant cultivation and processing"
  },
  {
    src: "/darvi-images/field6.jpg",
    title: "Integrated Farming Systems",
    description: "Combined crop and livestock farming for maximum efficiency"
  }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
}

const cardVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
}

function Home() {
  return (
    <HomeContainer>
      <ModernHeroSection />
      
      <CurvedDivider type="wave1" fillColor="white" opacity={1} />
      
      <InvestmentCalculator />
      
      <CurvedDivider type="wave2" fillColor="#f0f7eb" opacity={1} />
      
      <WhyInvestSection />
      
      <CurvedDivider type="wave3" fillColor="white" opacity={1} />
      
      <VideoSection />
      
      <CurvedDivider type="wave1" fillColor="#f8f9fa" opacity={1} />
      
      <ImageGallerySection
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 style={{ 
              textAlign: 'center', 
              marginBottom: '1rem',
              color: 'var(--primary-dark)',
              fontFamily: 'var(--font-heading)',
              fontSize: 'clamp(2rem, 4vw, 2.8rem)',
              fontWeight: '700'
            }}>
              Our Farmland Gallery
            </h2>
            <p style={{ 
              textAlign: 'center', 
              color: 'var(--text-muted)',
              fontSize: '1.2rem',
              maxWidth: '600px',
              margin: '0 auto',
              lineHeight: '1.7'
            }}>
              Explore our diverse portfolio of successful farmland projects showcasing modern agricultural practices and sustainable farming techniques.
            </p>
          </motion.div>
          
          <ImageGrid
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
          >
            {galleryImages.map((image, index) => (
              <ImageCard
                key={index}
                variants={cardVariants}
                style={{ backgroundImage: `url(${image.src})` }}
                whileHover={{ scale: 1.02 }}
              >
                <ImageOverlay>
                  <h4>{image.title}</h4>
                  <p>{image.description}</p>
                </ImageOverlay>
              </ImageCard>
            ))}
          </ImageGrid>
        </div>
      </ImageGallerySection>
      
      <CurvedDivider type="wave2" fillColor="#e8f5e3" opacity={1} />
      
      <PropertyShowcase />
      
      <CurvedDivider type="wave1" fillColor="white" opacity={1} />
      
      <InvestmentProcess />
      
      <CurvedDivider type="wave3" fillColor="#2e5d35" opacity={1} />
      
      <InvestmentStats />
      
      <CurvedDivider type="wave2" fillColor="#f8f9fa" opacity={1} />
      
      <FAQSection />
      
      <CurvedDivider type="wave1" fillColor="white" opacity={1} />
      
      <AwardsSection />
      
      <CurvedDivider type="wave3" fillColor="var(--primary-color)" opacity={0.1} />
    </HomeContainer>
  )
}

export default Home