backend:
  name: git-gateway
  branch: master # Branch to update (using master as per your repository)

# Publish mode options:
# - editorial_workflow: Adds an interface for drafting, reviewing, and approving content
publish_mode: editorial_workflow

# Media folder where uploaded images will be stored in the repo
media_folder: "public/images/uploads"
# Public folder where the images will be accessed in the built site
public_folder: "/images/uploads"

# Site URL for preview
site_url: http://localhost:5173
display_url: http://localhost:5173

# Logo for the CMS interface
logo_url: /darvi-icon.svg

# Editor settings
editor:
  preview: true

collections:
  # Home Page
  - name: "home"
    label: "🏠 Home Page"
    description: "Manage the main landing page content, hero section, and featured farmland projects"
    files:
      - name: "home"
        label: "Home Page Content"
        file: "src/content/home.json"
        fields:
          # Hero Section
          - { label: "🎯 Hero Title", name: "heroTitle", widget: "string", hint: "Main headline displayed on the hero section" }
          - { label: "🎯 Hero Subtitle", name: "heroSubtitle", widget: "text", hint: "Supporting text below the main title" }
          - { label: "🖼️ Hero Background Image", name: "heroBackgroundImage", widget: "image", hint: "Large background image for the hero section (recommended: 1920x1080px)", media_folder: "/public/images/uploads", public_folder: "/images/uploads" }
          - { label: "🔘 Primary Button Text", name: "primaryBtnText", widget: "string", hint: "Text for the main call-to-action button" }
          - { label: "🔗 Primary Button Link", name: "primaryBtnLink", widget: "string", hint: "URL or page path for the primary button" }
          - { label: "🔘 Secondary Button Text", name: "secondaryBtnText", widget: "string", hint: "Text for the secondary button" }
          - { label: "🔗 Secondary Button Link", name: "secondaryBtnLink", widget: "string", hint: "URL or page path for the secondary button" }
          # Farmland Projects Section
          - label: "🚜 Farmland Projects Section"
            name: "farmProjectsSection"
            widget: "object"
            collapsed: true
            hint: "Configure the featured farmland development projects section"
            fields:
              - { label: "Section Subtitle", name: "subtitle", widget: "string", hint: "Small text above the main title" }
              - { label: "Section Title", name: "title", widget: "string", hint: "Main section heading" }
              - { label: "Section Description", name: "description", widget: "text", hint: "Brief description of the farmland development projects" }
          - label: "🌱 Featured Projects"
            name: "featuredProjects"
            widget: "list"
            hint: "Add and manage featured farmland development projects (recommended: 3 projects)"
            fields:
              - { label: "Project Title", name: "title", widget: "string", hint: "Name of the farmland development project" }
              - { label: "Project Description", name: "description", widget: "text", hint: "Brief description of the project and its achievements" }
              - { label: "Project Link", name: "link", widget: "string", hint: "URL to project details page (e.g., /projects/1)" }
              - { label: "Project Status", name: "status", widget: "select", options: ["Upcoming", "Ongoing", "Completed"], hint: "Current status of the project" }
              - { label: "Project Image", name: "image", widget: "image", hint: "Featured image for the project (recommended: 800x600px)", media_folder: "/public/images/uploads", public_folder: "/images/uploads" }
          # Services Section
          - label: "🛠️ Services Section"
            name: "servicesSection"
            widget: "object"
            collapsed: true
            hint: "Configure the farmland development services section"
            fields:
              - { label: "Section Subtitle", name: "subtitle", widget: "string", hint: "Small text above the main title" }
              - { label: "Section Title", name: "title", widget: "string", hint: "Main section heading" }
              - { label: "Section Description", name: "description", widget: "text", hint: "Brief description of your farmland development services" }
          - label: "🔧 Services"
            name: "services"
            widget: "list"
            hint: "Add and manage your farmland development services (recommended: 4 services)"
            fields:
              - { label: "Service Title", name: "title", widget: "string", hint: "Name of the service" }
              - { label: "Service Description", name: "description", widget: "text", hint: "Detailed description of what this service offers" }
              - { label: "Service Icon", name: "icon", widget: "select", options: ["FaTractor", "FaUsers", "FaLeaf", "FaChartLine", "FaSeedling", "FaTree", "FaWater", "FaRecycle", "FaAward"], hint: "Choose an icon that represents this service" }
              - label: "Service Features"
                name: "items"
                widget: "list"
                required: false
                hint: "Optional: Add specific features or benefits of this service"
                field: { label: "Feature", name: "feature", widget: "string" }
          # About Section
          - label: "ℹ️ About Section"
            name: "aboutSection"
            widget: "object"
            collapsed: true
            hint: "Configure the about section on the home page"
            fields:
              - { label: "About Title", name: "title", widget: "string", hint: "Main heading for the about section" }
              - { label: "About Text", name: "text", widget: "markdown", hint: "Detailed description about your farmland development company (supports markdown formatting)" }
              - { label: "About Image", name: "image", widget: "image", hint: "Image for the about section (recommended: 800x600px)", media_folder: "/public/images/uploads", public_folder: "/images/uploads", required: false }
              - { label: "Button Text", name: "buttonText", widget: "string", hint: "Text for the call-to-action button" }
              - { label: "Button Link", name: "buttonLink", widget: "string", hint: "URL or page path for the button" }
          # Call-to-Action Section
          - label: "📢 Call-to-Action Section"
            name: "ctaSection"
            widget: "object"
            collapsed: true
            hint: "Configure the final call-to-action section"
            fields:
              - { label: "CTA Title", name: "title", widget: "string", hint: "Compelling headline for the call-to-action" }
              - { label: "CTA Text", name: "text", widget: "text", hint: "Supporting text that encourages action" }
              - { label: "CTA Button Text", name: "buttonText", widget: "string", hint: "Text for the action button" }
              - { label: "CTA Button Link", name: "buttonLink", widget: "string", hint: "URL or page path for the button" }
              - { label: "CTA Background Image", name: "backgroundImage", widget: "image", hint: "Background image for the CTA section (recommended: 1920x1080px)", media_folder: "/public/images/uploads", public_folder: "/images/uploads" }

  # Projects Page
  - name: "projects"
    label: "🚜 Projects Page"
    description: "Manage farmland development projects, locations, and project details"
    files:
      - name: "projects"
        label: "Projects Page Content"
        file: "src/content/projects.json"
        fields:
          # Hero Section
          - { label: "🎯 Hero Title", name: "heroTitle", widget: "string", hint: "Main headline for the projects page", default: "Farmland Projects" }
          - { label: "🎯 Hero Subtitle", name: "heroSubtitle", widget: "text", hint: "Supporting text describing your farmland projects", default: "Discover our comprehensive portfolio of ongoing, completed, and upcoming farmland development projects across diverse agricultural landscapes." }
          - { label: "🖼️ Hero Background Image", name: "heroBackgroundImage", widget: "image", hint: "Background image for projects page hero (recommended: 1920x1080px)", media_folder: "/public/images/uploads", public_folder: "/images/uploads" }
          - { label: "🔘 Primary Button Text", name: "primaryBtnText", widget: "string", hint: "Text for the main button", default: "View Project Map" }
          - { label: "🔗 Primary Button Link", name: "primaryBtnLink", widget: "string", hint: "URL for the main button", default: "#map" }
          # Project Summary Section
          - label: "📊 Project Summary Section"
            name: "projectSummarySection"
            widget: "object"
            collapsed: true
            hint: "Configure the project overview statistics section"
            fields:
              - { label: "Section Subtitle", name: "subtitle", widget: "string", hint: "Small text above the main title", default: "Project Overview" }
              - { label: "Section Title", name: "title", widget: "string", hint: "Main section heading", default: "Our Development Portfolio" }
              - { label: "Section Description", name: "description", widget: "text", hint: "Brief description of the project portfolio", default: "Comprehensive farmland development projects across India" }
          # Projects List Section
          - label: "🌾 Projects List Section"
            name: "projectsListSection"
            widget: "object"
            collapsed: true
            hint: "Configure the main projects listing section"
            fields:
              - { label: "Section Subtitle", name: "subtitle", widget: "string", hint: "Small text above the main title", default: "Project Portfolio" }
              - { label: "Section Title", name: "title", widget: "string", hint: "Main section heading", default: "Farmland Development Projects" }
              - { label: "Section Description", name: "description", widget: "text", hint: "Brief description of the projects", default: "Explore our comprehensive collection of ongoing projects, completed developments, and upcoming farmland initiatives." }
          # Map Section
          - label: "🗺️ Map Section"
            name: "mapSection"
            widget: "object"
            collapsed: true
            hint: "Configure the interactive map section"
            fields:
              - { label: "Section Subtitle", name: "subtitle", widget: "string", hint: "Small text above the main title", default: "Project Locations" }
              - { label: "Section Title", name: "title", widget: "string", hint: "Main section heading", default: "Interactive Project Map" }
              - { label: "Section Description", name: "description", widget: "text", hint: "Brief description of the map", default: "Explore the geographical distribution of our farmland development projects across India." }

  # Individual Projects Data
  - name: "project-data"
    label: "🌱 Project Data"
    description: "Manage individual farmland development projects with detailed information, images, and location data"
    folder: "src/content/projects"
    create: true
    slug: "project-{{slug}}"
    fields:
      # Basic Project Information
      - { label: "Project ID", name: "id", widget: "number", hint: "Unique project identifier (1, 2, 3, etc.)" }
      - { label: "Project Title", name: "title", widget: "string", hint: "Full name of the farmland development project" }
      - { label: "Project Description", name: "description", widget: "markdown", hint: "Comprehensive description of the project (supports markdown formatting)" }
      - { label: "Project Status", name: "status", widget: "select", options: ["Upcoming", "Ongoing", "Completed"], hint: "Current status of the project" }
      - { label: "Project Type", name: "type", widget: "string", hint: "Type of farming/agriculture (e.g., Mixed Agriculture, Organic Farming)" }
      # Location Information
      - label: "📍 Location Details"
        name: "location"
        widget: "object"
        collapsed: false
        hint: "Geographic and address information for the project"
        fields:
          - { label: "Full Address", name: "address", widget: "string", hint: "Complete address including city, state, country" }
          - { label: "City", name: "city", widget: "string", hint: "City where the project is located" }
          - { label: "State", name: "state", widget: "string", hint: "State/province where the project is located" }
          - { label: "Country", name: "country", widget: "string", hint: "Country where the project is located", default: "India" }
          - { label: "Latitude", name: "latitude", widget: "number", value_type: "float", hint: "Latitude coordinate for map display" }
          - { label: "Longitude", name: "longitude", widget: "number", value_type: "float", hint: "Longitude coordinate for map display" }
      # Project Details
      - label: "📋 Project Details"
        name: "details"
        widget: "object"
        collapsed: false
        hint: "Detailed project information and specifications"
        fields:
          - { label: "Total Area", name: "area", widget: "string", hint: "Total project area (e.g., '150 acres', '200 hectares')" }
          - { label: "Start Date", name: "startDate", widget: "string", hint: "Project start date (e.g., 'January 2024')" }
          - { label: "Expected Completion", name: "expectedCompletion", widget: "string", hint: "Expected completion date (e.g., 'December 2025')" }
          - { label: "Total Investment", name: "investment", widget: "string", hint: "Total project investment (e.g., '₹2.5 Crores')" }
          - { label: "Expected ROI", name: "expectedROI", widget: "string", hint: "Expected return on investment (e.g., '15-20% annually')" }
      # Project Images
      - label: "🖼️ Project Images"
        name: "images"
        widget: "object"
        collapsed: true
        hint: "Images and visual content for the project"
        fields:
          - { label: "Hero Image", name: "heroImage", widget: "image", hint: "Main project image for hero sections (recommended: 1920x1080px)", media_folder: "/public/images/uploads", public_folder: "/images/uploads" }
          - { label: "Featured Image", name: "featuredImage", widget: "image", hint: "Featured image for project cards (recommended: 800x600px)", media_folder: "/public/images/uploads", public_folder: "/images/uploads" }
          - label: "📸 Gallery Images"
            name: "galleryImages"
            widget: "list"
            hint: "Additional images showcasing the project progress and features"
            field: { label: "Gallery Image", name: "image", widget: "image", media_folder: "/public/images/uploads", public_folder: "/images/uploads" }
      # Project Features
      - label: "⭐ Project Features"
        name: "features"
        widget: "list"
        hint: "Key features and highlights of the project (recommended: 3-6 features)"
        fields:
          - { label: "Feature Title", name: "title", widget: "string", hint: "Name of the feature" }
          - { label: "Feature Description", name: "description", widget: "text", hint: "Detailed description of this feature" }
          - { label: "Feature Icon", name: "icon", widget: "select", options: ["FaLeaf", "FaUsers", "FaChartLine", "FaTractor", "FaWater", "FaSeedling", "FaTree", "FaRecycle", "FaAward"], hint: "Icon representing this feature" }



  # Contact Page
  - name: "contact"
    label: "📞 Contact Page"
    description: "Manage contact information and location details for farmland inquiries"
    files:
      - name: "contact"
        label: "Contact Page Content"
        file: "src/content/contact.json"
        fields:
          # Hero Section
          - { label: "🎯 Hero Title", name: "heroTitle", widget: "string", hint: "Main headline for the contact page", default: "Contact Darvi Farmlands" }
          - { label: "🎯 Hero Subtitle", name: "heroSubtitle", widget: "text", hint: "Supporting text encouraging contact", default: "Get in touch with our farmland development experts for project inquiries and consultations" }
          - { label: "🖼️ Hero Background Image", name: "heroBackgroundImage", widget: "image", hint: "Background image for contact page hero (recommended: 1920x1080px)", media_folder: "/public/images/uploads", public_folder: "/images/uploads" }
          # Contact Information
          - label: "📋 Contact Information"
            name: "contactInfo"
            widget: "object"
            collapsed: false
            hint: "Main contact details for your farmland development business"
            fields:
              - { label: "Business Address", name: "address", widget: "text", hint: "Full business address including city and postal code" }
              - { label: "Phone Number", name: "phone", widget: "string", hint: "Primary contact phone number" }
              - { label: "Email Address", name: "email", widget: "string", hint: "Primary contact email address" }
              - { label: "Business Hours", name: "hours", widget: "text", hint: "Operating hours (e.g., 'Mon-Fri: 9AM-6PM')" }
          # Map Location
          - label: "🗺️ Office Map Location"
            name: "mapLocation"
            widget: "object"
            collapsed: true
            hint: "Geographic coordinates for office location map display"
            fields:
              - { label: "Latitude", name: "latitude", widget: "number", value_type: "float", hint: "Latitude coordinate for office map location" }
              - { label: "Longitude", name: "longitude", widget: "number", value_type: "float", hint: "Longitude coordinate for office map location" }

  # Global Site Settings
  - name: "settings"
    label: "⚙️ Site Settings"
    description: "Manage global site settings, navigation, and branding for Darvi Farmlands"
    files:
      - name: "site-settings"
        label: "Global Site Settings"
        file: "src/content/site-settings.json"
        fields:
          # Site Information
          - label: "🏢 Site Information"
            name: "siteInfo"
            widget: "object"
            collapsed: false
            hint: "Basic information about your farmland development website"
            fields:
              - { label: "Site Title", name: "title", widget: "string", hint: "Main title of your website", default: "Darvi Farmlands - Sustainable Agricultural Development" }
              - { label: "Site Description", name: "description", widget: "text", hint: "Brief description for SEO and social media", default: "Your trusted partner in comprehensive farmland development and agricultural project management since 2018." }
              - { label: "Company Name", name: "companyName", widget: "string", hint: "Official company name", default: "Darvi Farmlands" }
              - { label: "Site Logo", name: "logo", widget: "image", hint: "Main logo for the website (recommended: 200x60px)", media_folder: "/public/images/uploads", public_folder: "/images/uploads", required: false }
          # Navigation
          - label: "🧭 Navigation Menu"
            name: "navigation"
            widget: "list"
            hint: "Manage main navigation menu items (currently: Home, Projects, Contact)"
            fields:
              - { label: "Menu Item Label", name: "label", widget: "string", hint: "Text displayed in the menu" }
              - { label: "Menu Item URL", name: "url", widget: "string", hint: "Page path or URL (e.g., '/', '/projects', '/contact')" }
              - { label: "Menu Item Order", name: "order", widget: "number", hint: "Order in which this item appears (1, 2, 3, etc.)" }
          # Social Media
          - label: "📱 Social Media Links"
            name: "socialMedia"
            widget: "object"
            collapsed: true
            hint: "Social media profiles and contact links"
            fields:
              - { label: "Facebook URL", name: "facebook", widget: "string", required: false, hint: "Full Facebook page URL" }
              - { label: "Instagram URL", name: "instagram", widget: "string", required: false, hint: "Full Instagram profile URL" }
              - { label: "Twitter URL", name: "twitter", widget: "string", required: false, hint: "Full Twitter profile URL" }
              - { label: "LinkedIn URL", name: "linkedin", widget: "string", required: false, hint: "Full LinkedIn company page URL" }
              - { label: "YouTube URL", name: "youtube", widget: "string", required: false, hint: "Full YouTube channel URL" }
          # SEO Settings
          - label: "🔍 SEO Settings"
            name: "seo"
            widget: "object"
            collapsed: true
            hint: "Search engine optimization settings for farmland development"
            fields:
              - { label: "Default Meta Description", name: "metaDescription", widget: "text", hint: "Default description for pages without specific meta descriptions", default: "Darvi Farmlands - Your trusted partner in comprehensive farmland development and agricultural project management since 2018." }
              - { label: "Keywords", name: "keywords", widget: "string", hint: "Comma-separated keywords for SEO", default: "farmland development, agricultural projects, sustainable farming, farmland investment, agricultural development, farm management, rural development, Darvi Farmlands" }
              - { label: "Open Graph Image", name: "ogImage", widget: "image", hint: "Default image for social media sharing (recommended: 1200x630px)", media_folder: "/public/images/uploads", public_folder: "/images/uploads", required: false }

  # Map Settings
  - name: "map-settings"
    label: "🗺️ Map Settings"
    description: "Configure interactive map settings, default locations, and map styling"
    files:
      - name: "map-settings"
        label: "Interactive Map Configuration"
        file: "src/content/map-settings.json"
        fields:
          # Default Map Settings
          - label: "🌍 Default Map Settings"
            name: "defaultSettings"
            widget: "object"
            collapsed: false
            hint: "Default settings for all interactive maps"
            fields:
              - { label: "Default Center Latitude", name: "centerLat", widget: "number", value_type: "float", hint: "Default latitude for map center (India: 20.5937)", default: 20.5937 }
              - { label: "Default Center Longitude", name: "centerLng", widget: "number", value_type: "float", hint: "Default longitude for map center (India: 78.9629)", default: 78.9629 }
              - { label: "Default Zoom Level", name: "defaultZoom", widget: "number", hint: "Default zoom level for overview maps (1-18)", default: 5 }
              - { label: "Project Detail Zoom", name: "projectZoom", widget: "number", hint: "Zoom level for individual project maps (1-18)", default: 10 }
          # Map Styling
          - label: "🎨 Map Styling"
            name: "styling"
            widget: "object"
            collapsed: true
            hint: "Visual styling options for the maps"
            fields:
              - { label: "Map Tile Provider", name: "tileProvider", widget: "select", options: ["OpenStreetMap", "Satellite", "Terrain"], hint: "Choose the map tile style", default: "OpenStreetMap" }
              - { label: "Marker Color", name: "markerColor", widget: "string", hint: "Color for project markers (hex code)", default: "#4a7c59" }
              - { label: "Map Height (Desktop)", name: "mapHeightDesktop", widget: "number", hint: "Map height in pixels for desktop", default: 500 }
              - { label: "Map Height (Mobile)", name: "mapHeightMobile", widget: "number", hint: "Map height in pixels for mobile", default: 300 }

  # Theme Settings
  - name: "theme-settings"
    label: "🎨 Theme Settings"
    description: "Manage website colors, fonts, and visual styling for the farmland theme"
    files:
      - name: "theme-settings"
        label: "Website Theme Configuration"
        file: "src/content/theme-settings.json"
        fields:
          # Color Palette
          - label: "🌈 Color Palette"
            name: "colors"
            widget: "object"
            collapsed: false
            hint: "Main color scheme for the farmland theme"
            fields:
              - { label: "Primary Color", name: "primary", widget: "string", hint: "Main brand color (hex code)", default: "#2d5016" }
              - { label: "Primary Light", name: "primaryLight", widget: "string", hint: "Lighter shade of primary (hex code)", default: "#4a7c59" }
              - { label: "Primary Dark", name: "primaryDark", widget: "string", hint: "Darker shade of primary (hex code)", default: "#1a3009" }
              - { label: "Accent Color", name: "accent", widget: "string", hint: "Accent color for highlights (hex code)", default: "#f4a261" }
              - { label: "Harvest Gold", name: "harvestGold", widget: "string", hint: "Gold color for agricultural elements (hex code)", default: "#ffc107" }
              - { label: "Earth Brown", name: "earthBrown", widget: "string", hint: "Brown color for earth elements (hex code)", default: "#8b4513" }
          # Background Images
          - label: "🖼️ Background Images"
            name: "backgroundImages"
            widget: "object"
            collapsed: true
            hint: "Default background images for various sections"
            fields:
              - { label: "Default Hero Background", name: "defaultHero", widget: "image", hint: "Default hero section background (recommended: 1920x1080px)", media_folder: "/public/images/uploads", public_folder: "/images/uploads" }
              - { label: "Projects Background", name: "projectsBackground", widget: "image", hint: "Background for projects sections (recommended: 1920x1080px)", media_folder: "/public/images/uploads", public_folder: "/images/uploads", required: false }
              - { label: "Contact Background", name: "contactBackground", widget: "image", hint: "Background for contact page (recommended: 1920x1080px)", media_folder: "/public/images/uploads", public_folder: "/images/uploads", required: false }
          # Typography
          - label: "📝 Typography"
            name: "typography"
            widget: "object"
            collapsed: true
            hint: "Font settings and text styling"
            fields:
              - { label: "Heading Font", name: "headingFont", widget: "select", options: ["Playfair Display", "Merriweather", "Lora", "Roboto Slab"], hint: "Font for headings and titles", default: "Playfair Display" }
              - { label: "Body Font", name: "bodyFont", widget: "select", options: ["Inter", "Open Sans", "Lato", "Source Sans Pro"], hint: "Font for body text", default: "Inter" }
              - { label: "Accent Font", name: "accentFont", widget: "select", options: ["Poppins", "Montserrat", "Nunito", "Raleway"], hint: "Font for special elements", default: "Poppins" }
