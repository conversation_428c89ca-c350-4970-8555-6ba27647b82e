import React from 'react';
import { StyleSheetInjector } from '../cms.jsx';
import styled from 'styled-components';
import { markdownToHtml } from '../../utils/cmsUtils';

const PreviewContainer = styled.div`
  font-family: 'Montserrat', sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
`;

const HeroPreview = styled.div`
  background-color: #f5f5f5;
  background-image: ${props => props.backgroundImage ? `url(${props.backgroundImage})` : 'none'};
  background-size: cover;
  background-position: center;
  padding: 100px 20px;
  text-align: center;
  color: white;
  border-radius: 8px;
  margin-bottom: 30px;
`;

const HeroTitle = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 1rem;
`;

const HeroSubtitle = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
`;

const SectionTitle = styled.div`
  text-align: center;
  margin-bottom: 30px;
`;

const SectionSubtitle = styled.p`
  color: #4caf50;
  text-transform: uppercase;
  font-weight: 600;
  margin-bottom: 0.5rem;
`;

const SectionHeading = styled.h2`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const SectionDescription = styled.p`
  max-width: 700px;
  margin: 0 auto;
  opacity: 0.8;
`;

const ContentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.8;

  h2 {
    color: #4caf50;
    margin-top: 30px;
    margin-bottom: 15px;
  }

  p {
    margin-bottom: 15px;
  }

  ul, ol {
    margin-bottom: 15px;
    padding-left: 20px;
  }

  li {
    margin-bottom: 5px;
  }
`;

const ApproachPagePreview = ({ entry }) => {
  const data = entry.getIn(['data']).toJS();

  return (
    <StyleSheetInjector>
      <PreviewContainer>
        <h2>Approach Page Preview</h2>

        <HeroPreview backgroundImage={data.heroBackgroundImage}>
          <HeroTitle>{data.heroTitle || 'Our Approach'}</HeroTitle>
          <HeroSubtitle>{data.heroSubtitle || 'Learn about our farming philosophy, technology integration, and sustainable practices.'}</HeroSubtitle>
          <div>
            <button>{data.primaryBtnText || 'Primary Button'}</button>
          </div>
        </HeroPreview>

        {data.methodsSection && (
          <div>
            <SectionTitle>
              <SectionSubtitle>{data.methodsSection.subtitle || 'Farming Philosophy'}</SectionSubtitle>
              <SectionHeading>{data.methodsSection.title || 'How We Manage Farms'}</SectionHeading>
              <SectionDescription>{data.methodsSection.description || 'Description'}</SectionDescription>
            </SectionTitle>

            <ContentContainer dangerouslySetInnerHTML={{ __html: markdownToHtml(data.content || '') }} />
          </div>
        )}
      </PreviewContainer>
    </StyleSheetInjector>
  );
};

export default ApproachPagePreview;
