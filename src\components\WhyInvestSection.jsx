import { motion } from 'framer-motion'
import styled from 'styled-components'
import { FaShieldAlt, FaChartLine, FaLeaf, FaUsers, FaClock, FaAward } from 'react-icons/fa'
import SectionTitle from './SectionTitle'

const WhyInvestSection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(to bottom, #ffffff, #f8f9fa);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1.5" fill="%234a7c59" fill-opacity="0.08"/><circle cx="80" cy="80" r="1.5" fill="%234a7c59" fill-opacity="0.08"/><circle cx="50" cy="50" r="2" fill="%234a7c59" fill-opacity="0.08"/></svg>');
    background-size: 100px 100px;
    pointer-events: none;
  }
`

const BenefitsGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2.5rem;
  position: relative;
  z-index: 1;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
`

const BenefitCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2.5rem;
  text-align: center;
  border: 1px solid rgba(74, 124, 89, 0.1);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
  }
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(74, 124, 89, 0.15);
    background: rgba(255, 255, 255, 1);
    
    &::before {
      transform: scaleX(1);
    }
  }
  
  @media (max-width: 768px) {
    padding: 2rem;
    
    &:hover {
      transform: translateY(-4px);
    }
  }
`

const IconWrapper = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  svg {
    font-size: 2rem;
    color: white;
    transition: transform 0.3s ease;
  }
  
  ${BenefitCard}:hover & {
    svg {
      transform: scale(1.1);
    }
    
    &::after {
      opacity: 1;
    }
  }
  
  @media (max-width: 768px) {
    width: 60px;
    height: 60px;
    
    svg {
      font-size: 1.5rem;
    }
  }
`

const BenefitTitle = styled.h3`
  margin-bottom: 1rem;
  color: var(--primary-dark);
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1.4rem;
  
  @media (max-width: 768px) {
    font-size: 1.2rem;
  }
`

const BenefitDescription = styled.p`
  color: var(--text-muted);
  line-height: 1.6;
  font-size: 1rem;
  margin-bottom: 1.5rem;
`

const BenefitHighlight = styled.div`
  background: linear-gradient(135deg, rgba(74, 124, 89, 0.1), rgba(139, 195, 74, 0.1));
  border-radius: 12px;
  padding: 1rem;
  font-weight: 600;
  color: var(--primary-color);
  font-size: 1.1rem;
`

const FloatingElement = styled(motion.div)`
  position: absolute;
  z-index: 0;
  opacity: 0.6;
  pointer-events: none;
  color: var(--primary-color);
`

const benefits = [
  {
    icon: <FaShieldAlt />,
    title: "Guaranteed Returns",
    description: "Secure your investment with our guaranteed 12% annual returns backed by professional farm management and proven agricultural practices.",
    highlight: "12% Annual Returns"
  },
  {
    icon: <FaChartLine />,
    title: "Steady Growth",
    description: "Watch your investment grow consistently with our transparent reporting and regular updates on farm performance and market conditions.",
    highlight: "Consistent Growth"
  },
  {
    icon: <FaLeaf />,
    title: "Sustainable Farming",
    description: "Invest in environmentally responsible agriculture that promotes soil health, biodiversity, and sustainable farming practices for future generations.",
    highlight: "Eco-Friendly"
  },
  {
    icon: <FaUsers />,
    title: "Expert Management",
    description: "Our team of agricultural experts and farm managers ensure optimal crop yields and efficient resource utilization for maximum returns.",
    highlight: "Professional Team"
  },
  {
    icon: <FaClock />,
    title: "Flexible Terms",
    description: "Choose investment durations that suit your financial goals, from short-term 1-year plans to long-term 5-year investment strategies.",
    highlight: "1-5 Year Terms"
  },
  {
    icon: <FaAward />,
    title: "Proven Track Record",
    description: "Join hundreds of satisfied investors who have already benefited from our successful farmland investment programs and agricultural expertise.",
    highlight: "500+ Investors"
  }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
}

function WhyInvest() {
  return (
    <WhyInvestSection
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
    >
      {/* Floating decorative elements */}
      <FloatingElement
        style={{ top: '10%', left: '5%' }}
        animate={{
          y: [0, -20, 0],
          rotate: [0, 5, 0]
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <FaLeaf size={40} />
      </FloatingElement>
      
      <FloatingElement
        style={{ top: '60%', right: '8%' }}
        animate={{
          y: [0, -15, 0],
          rotate: [0, -5, 0]
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
      >
        <FaChartLine size={35} />
      </FloatingElement>

      <div className="container">
        <SectionTitle
          subtitle="Why Choose Us"
          title="Why Invest with Mogg's Estates?"
          description="Discover the advantages of investing in professionally managed farmland with guaranteed returns and sustainable practices."
        />
        
        <BenefitsGrid
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
        >
          {benefits.map((benefit, index) => (
            <BenefitCard
              key={index}
              variants={itemVariants}
              whileHover={{ scale: 1.02 }}
            >
              <IconWrapper>
                {benefit.icon}
              </IconWrapper>
              <BenefitTitle>{benefit.title}</BenefitTitle>
              <BenefitDescription>{benefit.description}</BenefitDescription>
              <BenefitHighlight>{benefit.highlight}</BenefitHighlight>
            </BenefitCard>
          ))}
        </BenefitsGrid>
      </div>
    </WhyInvestSection>
  )
}

export default WhyInvest