import { motion } from 'framer-motion'
import styled from 'styled-components'
import { FaLaptop, FaSearch, FaHandshake, FaChartLine, FaArrowRight } from 'react-icons/fa'
import SectionTitle from './SectionTitle'

const ProcessSection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(to bottom, #ffffff, #f0f7eb);
  position: relative;
  overflow: hidden;
`

const ProcessContainer = styled.div`
  position: relative;
  z-index: 1;
`

const ProcessGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  
  @media (max-width: 768px) {
    gap: 2rem;
  }
`

const ProcessStep = styled(motion.div)`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  
  &:nth-child(even) {
    .process-content {
      order: 2;
    }
    .process-image {
      order: 1;
    }
  }
  
  @media (max-width: 968px) {
    grid-template-columns: 1fr;
    gap: 2rem;
    
    &:nth-child(even) {
      .process-content {
        order: 1;
      }
      .process-image {
        order: 2;
      }
    }
  }
`

const ProcessContent = styled.div`
  position: relative;
  padding: 2rem;
  
  @media (max-width: 768px) {
    padding: 1rem;
  }
`

const StepNumber = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  box-shadow: 0 8px 20px rgba(74, 124, 89, 0.3);
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    z-index: -1;
    opacity: 0.3;
    animation: pulse 2s ease-in-out infinite;
  }
  
  @keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.1); opacity: 0.1; }
  }
  
  @media (max-width: 768px) {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
`

const StepTitle = styled.h3`
  margin-bottom: 1rem;
  color: var(--primary-dark);
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 2rem;
  line-height: 1.2;
  
  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`

const StepDescription = styled.p`
  color: var(--text-muted);
  line-height: 1.7;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  
  @media (max-width: 768px) {
    font-size: 1rem;
  }
`

const StepFeatures = styled.ul`
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
  
  li {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: var(--text-dark);
    font-size: 1rem;
    
    &::before {
      content: '✓';
      color: var(--primary-color);
      font-weight: bold;
      margin-right: 1rem;
      font-size: 1.1rem;
    }
  }
`

const ProcessImage = styled(motion.div)`
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  height: 400px;
  background-size: cover;
  background-position: center;
  box-shadow: 0 20px 40px rgba(74, 124, 89, 0.15);
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(74, 124, 89, 0.1), rgba(139, 195, 74, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover::before {
    opacity: 1;
  }
  
  @media (max-width: 768px) {
    height: 300px;
  }
`

const ProcessButton = styled(motion.button)`
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(74, 124, 89, 0.3);
  }
  
  svg {
    transition: transform 0.3s ease;
  }
  
  &:hover svg {
    transform: translateX(4px);
  }
`

const ConnectorLine = styled.div`
  position: absolute;
  left: 50%;
  top: 100%;
  width: 2px;
  height: 3rem;
  background: linear-gradient(to bottom, var(--primary-color), transparent);
  transform: translateX(-50%);
  z-index: 0;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
  }
  
  @media (max-width: 968px) {
    display: none;
  }
`

const processSteps = [
  {
    number: "01",
    title: "Choose Your Investment",
    description: "Browse our curated selection of premium farmland properties and select the investment amount that suits your financial goals.",
    features: [
      "Minimum investment from ₹1 Lakh",
      "Flexible investment amounts",
      "Detailed property information",
      "ROI calculator available"
    ],
    image: "https://images.unsplash.com/photo-1629339942248-45d4b10c8c2f?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwzfHxsYXB0b3AlMjBpbnZlc3RtZW50JTIwdGVjaG5vbG9neSUyMGJ1c2luZXNzfGVufDB8MHx8fDE3NTQ0MTY3MTh8MA&ixlib=rb-4.1.0&q=85",
    buttonText: "Start Investing"
  },
  {
    number: "02", 
    title: "Site Visit & Verification",
    description: "Our experts conduct thorough site inspections and provide you with detailed reports on soil quality, water availability, and growth potential.",
    features: [
      "Professional site inspection",
      "Soil quality analysis",
      "Water source verification", 
      "Growth potential assessment"
    ],
    image: "https://images.unsplash.com/photo-1708921371195-10e57932622b?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw4fHxsYW5kJTIwc3VydmV5JTIwYWdyaWN1bHR1cmUlMjBpbnNwZWN0aW9uJTIwZmFybWxhbmR8ZW58MHwwfHxncmVlbnwxNzU0NDE2NzE3fDA&ixlib=rb-4.1.0&q=85",
    buttonText: "Schedule Visit"
  },
  {
    number: "03",
    title: "Secure Your Investment", 
    description: "Complete the investment process with our secure payment system and receive all necessary documentation and ownership certificates.",
    features: [
      "Secure payment gateway",
      "Legal documentation",
      "Ownership certificates",
      "Investment confirmation"
    ],
    image: "https://images.unsplash.com/photo-1647507490306-3411f230a750?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwyfHxsYXB0b3AlMjBpbnZlc3RtZW50JTIwdGVjaG5vbG9neSUyMGJ1c2luZXNzfGVufDB8MHx8fDE3NTQ0MTY3MTh8MA&ixlib=rb-4.1.0&q=85",
    buttonText: "Secure Investment"
  },
  {
    number: "04",
    title: "Watch Your Investment Grow",
    description: "Monitor your investment performance through our dashboard with regular updates, progress reports, and guaranteed returns.",
    features: [
      "Real-time progress tracking",
      "Monthly progress reports", 
      "Guaranteed 12% returns",
      "24/7 customer support"
    ],
    image: "https://images.unsplash.com/photo-1591018377455-aafbddc2eec7?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw2fHxtb2Rlcm4lMjBmYXJtJTIwaXJyaWdhdGlvbiUyMGFncmljdWx0dXJlJTIwdGVjaG5vbG9neXxlbnwwfDB8fGdyZWVufDE3NTQ0MTY3MTd8MA&ixlib=rb-4.1.0&q=85",
    buttonText: "Track Progress"
  }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.3,
      delayChildren: 0.2
    }
  }
}

const stepVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut"
    }
  }
}

function InvestmentProcess() {
  return (
    <ProcessSection
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
    >
      <div className="container">
        <SectionTitle
          subtitle="Investment Process"
          title="With Us, Your Investment Works Harder, Giving You Confidence and Peace of Mind"
          description="Our streamlined investment process ensures transparency, security, and maximum returns for your farmland investments."
        />
        
        <ProcessContainer>
          <ProcessGrid
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
          >
            {processSteps.map((step, index) => (
              <div key={step.number} style={{ position: 'relative' }}>
                <ProcessStep variants={stepVariants}>
                  <ProcessContent className="process-content">
                    <StepNumber>{step.number}</StepNumber>
                    <StepTitle>{step.title}</StepTitle>
                    <StepDescription>{step.description}</StepDescription>
                    <StepFeatures>
                      {step.features.map((feature, idx) => (
                        <li key={idx}>{feature}</li>
                      ))}
                    </StepFeatures>
                    <ProcessButton
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      {step.buttonText}
                      <FaArrowRight />
                    </ProcessButton>
                  </ProcessContent>
                  
                  <ProcessImage 
                    className="process-image"
                    style={{ backgroundImage: `url(${step.image})` }}
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.3 }}
                  />
                </ProcessStep>
                
                {index < processSteps.length - 1 && <ConnectorLine />}
              </div>
            ))}
          </ProcessGrid>
        </ProcessContainer>
      </div>
    </ProcessSection>
  )
}

export default InvestmentProcess