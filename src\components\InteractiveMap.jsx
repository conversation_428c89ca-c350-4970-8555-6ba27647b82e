import { useEffect } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet'
import L from 'leaflet'
import styled from 'styled-components'
import 'leaflet/dist/leaflet.css'

// Fix for default markers in React Leaflet
delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

const MapWrapper = styled.div`
  height: 400px;
  width: 100%;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-medium);

  @media (max-width: 768px) {
    height: 300px;
  }

  .leaflet-container {
    height: 100%;
    width: 100%;
  }

  .leaflet-popup-content-wrapper {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
  }

  .leaflet-popup-content {
    margin: 1rem;
    font-family: var(--font-body);

    @media (max-width: 768px) {
      margin: 0.5rem;
    }
  }

  .leaflet-control-zoom {
    @media (max-width: 768px) {
      transform: scale(0.8);
    }
  }
`

const PopupContent = styled.div`
  min-width: 200px;

  h4 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
  }

  p {
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
  }

  .status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-top: 0.5rem;

    &.ongoing {
      background-color: var(--accent-color);
      color: white;
    }

    &.completed {
      background-color: var(--success-color);
      color: white;
    }

    &.upcoming {
      background-color: var(--info-color);
      color: white;
    }
  }

  @media (max-width: 768px) {
    min-width: 180px;

    h4 {
      font-size: 1rem;
    }

    p {
      font-size: 0.85rem;
    }

    .status {
      font-size: 0.75rem;
      padding: 0.2rem 0.6rem;
    }
  }
`

// Sample project locations across India
const projectLocations = [
  {
    id: 1,
    title: 'Green Valley Farmland Development',
    position: [12.9716, 77.5946], // Bangalore, Karnataka
    status: 'Ongoing',
    area: '150 acres',
    location: 'Karnataka, India'
  },
  {
    id: 2,
    title: 'Sunrise Agricultural Estate',
    position: [11.1271, 78.6569], // Salem, Tamil Nadu
    status: 'Ongoing',
    area: '200 acres',
    location: 'Tamil Nadu, India'
  },
  {
    id: 3,
    title: 'Blue River Agro Park',
    position: [15.9129, 79.7400], // Kurnool, Andhra Pradesh
    status: 'Upcoming',
    area: '300 acres',
    location: 'Andhra Pradesh, India'
  },
  {
    id: 4,
    title: 'Highland Organic Farms',
    position: [10.8505, 76.2711], // Palakkad, Kerala
    status: 'Completed',
    area: '180 acres',
    location: 'Kerala, India'
  },
  {
    id: 5,
    title: 'Meadow Brook Agricultural Hub',
    position: [19.7515, 75.7139], // Aurangabad, Maharashtra
    status: 'Ongoing',
    area: '250 acres',
    location: 'Maharashtra, India'
  },
  {
    id: 6,
    title: 'Sunset Valley Farmlands',
    position: [31.6340, 74.8723], // Amritsar, Punjab
    status: 'Completed',
    area: '400 acres',
    location: 'Punjab, India'
  },
  {
    id: 7,
    title: 'Evergreen Agro Complex',
    position: [23.0225, 72.5714], // Ahmedabad, Gujarat
    status: 'Upcoming',
    area: '120 acres',
    location: 'Gujarat, India'
  },
  {
    id: 8,
    title: 'Golden Fields Estate',
    position: [29.0588, 76.0856], // Karnal, Haryana
    status: 'Ongoing',
    area: '220 acres',
    location: 'Haryana, India'
  },
  {
    id: 9,
    title: 'Riverside Agricultural Park',
    position: [26.8467, 80.9462], // Lucknow, Uttar Pradesh
    status: 'Completed',
    area: '160 acres',
    location: 'Uttar Pradesh, India'
  }
]

function InteractiveMap({ selectedProject = null, height = 400 }) {
  // Center map on India
  const center = [20.5937, 78.9629]
  const zoom = selectedProject ? 10 : 5

  // If a specific project is selected, center on it
  const mapCenter = selectedProject 
    ? projectLocations.find(p => p.id === selectedProject)?.position || center
    : center

  return (
    <MapWrapper style={{ height: `${height}px` }}>
      <MapContainer
        center={mapCenter}
        zoom={zoom}
        style={{ height: '100%', width: '100%' }}
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />
        
        {projectLocations.map((project) => (
          <Marker key={project.id} position={project.position}>
            <Popup>
              <PopupContent>
                <h4>{project.title}</h4>
                <p><strong>Location:</strong> {project.location}</p>
                <p><strong>Area:</strong> {project.area}</p>
                <span className={`status ${project.status.toLowerCase()}`}>
                  {project.status}
                </span>
              </PopupContent>
            </Popup>
          </Marker>
        ))}
      </MapContainer>
    </MapWrapper>
  )
}

export default InteractiveMap
