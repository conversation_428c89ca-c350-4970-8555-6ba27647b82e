# Darvi Group Website CMS Instructions

This document provides instructions on how to use the Content Management System (CMS) for the Darvi Group website. We're using Decap CMS (formerly known as Netlify CMS) for content management.

## Accessing the CMS

1. Go to your website URL followed by `/admin` (e.g., `https://darvi-group.netlify.app/admin`)
2. Log in using your Netlify Identity credentials
   - If you haven't set up Netlify Identity yet, you'll need to do that first in the Netlify dashboard

## Managing Content

The CMS is organized by pages, with each page having its own set of content fields.

### Home Page

The Home page content includes:
- Hero section (title, subtitle, background image, buttons)
- Farm Projects section
- Services section
- About section
- Stats section
- CTA (Call to Action) section

To edit the Home page:
1. Click on "Home Page" in the sidebar
2. Select "Home Page Content"
3. Make your changes
4. Click "Save" to save as a draft or "Publish" to publish immediately

### About Page

The About page content includes:
- Hero section
- Story section with content blocks
- Team section with team members
- Values section

### Other Pages

Similar editing interfaces are available for all other pages:
- Approach
- Farms
- Investment
- Technology
- Sustainability
- Resources
- Contact

## Working with Images

### Adding Images

1. When editing a content field that accepts an image, click the "Choose an image" button
2. You can either:
   - Upload a new image from your computer
   - Select a previously uploaded image from the media library

### Image Guidelines

- Use high-quality images (recommended minimum width: 1200px)
- Optimize images for web before uploading (compress to reduce file size)
- Use JPG format for photographs and PNG for graphics with transparency
- Keep file sizes under 500KB when possible for better performance

### Deleting Images

1. Go to the Media Library (click "Media" in the sidebar)
2. Hover over the image you want to delete
3. Click the delete icon (trash can)
4. Confirm deletion

Note: Be careful when deleting images that might be used in multiple places on the website.

## Publishing Workflow

The CMS uses an editorial workflow that includes three stages:
1. **Draft**: Initial content creation
2. **In Review**: Content ready for review
3. **Ready**: Content approved and ready to publish

To move content through the workflow:
1. Save your changes as a draft
2. Click "Set Status" and select the next status
3. When ready, click "Publish"

## Need Help?

If you encounter any issues or have questions about using the CMS, please contact your website administrator.
