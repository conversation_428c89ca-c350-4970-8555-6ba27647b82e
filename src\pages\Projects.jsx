import { useState } from 'react'
import { motion } from 'framer-motion'
import styled from 'styled-components'
import { FaMapMarkerAlt, FaRulerCombined, FaRupeeSign, FaCalendarAlt, FaLeaf, FaFilter } from 'react-icons/fa'
import SectionTitle from '../components/SectionTitle'
import CurvedDivider from '../components/CurvedDivider'

const ProjectsContainer = styled.div`
  padding-top: 100px;
  min-height: 100vh;
`

const HeroSection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(135deg, #f0f7eb 0%, #e8f5e3 100%);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%234a7c59" fill-opacity="0.1"/><circle cx="80" cy="80" r="2" fill="%234a7c59" fill-opacity="0.1"/></svg>');
    background-size: 100px 100px;
    pointer-events: none;
  }
`

const FilterSection = styled(motion.section)`
  padding: var(--spacing-xl) 0;
  background: white;
`

const FilterContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
`

const FilterButtons = styled.div`
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  
  @media (max-width: 768px) {
    justify-content: center;
  }
`

const FilterButton = styled(motion.button)`
  padding: 0.75rem 1.5rem;
  border: 2px solid ${props => props.active ? 'var(--primary-color)' : 'rgba(74, 124, 89, 0.2)'};
  background: ${props => props.active ? 'var(--primary-color)' : 'transparent'};
  color: ${props => props.active ? 'white' : 'var(--primary-color)'};
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
  }
`

const ProjectsGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2.5rem;
  padding: var(--spacing-xl) 0;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
`

const ProjectCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(74, 124, 89, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s ease;
  
  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 60px rgba(74, 124, 89, 0.2);
  }
`

const ProjectImage = styled.div`
  height: 280px;
  background-size: cover;
  background-position: center;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.1) 100%);
  }
`

const ProjectBadge = styled.div`
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  background: ${props => {
    switch(props.status) {
      case 'ongoing': return 'linear-gradient(135deg, #4caf50, #8bc34a)';
      case 'completed': return 'linear-gradient(135deg, #2196f3, #03a9f4)';
      case 'upcoming': return 'linear-gradient(135deg, #ff9800, #ffc107)';
      default: return 'linear-gradient(135deg, var(--primary-color), var(--primary-light))';
    }
  }};
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`

const ProjectContent = styled.div`
  padding: 2rem;
`

const ProjectTitle = styled.h3`
  margin-bottom: 1rem;
  color: var(--primary-dark);
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1.5rem;
`

const ProjectLocation = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  color: var(--text-muted);
  
  svg {
    margin-right: 0.5rem;
    color: var(--primary-color);
  }
`

const ProjectDetails = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
`

const DetailItem = styled.div`
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background: rgba(74, 124, 89, 0.05);
  border-radius: 12px;
  
  svg {
    margin-right: 0.75rem;
    color: var(--primary-color);
  }
  
  .value {
    font-weight: 600;
    color: var(--text-dark);
  }
`

const ProjectDescription = styled.p`
  color: var(--text-muted);
  line-height: 1.6;
  margin-bottom: 2rem;
`

const ProjectButton = styled(motion.button)`
  width: 100%;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(74, 124, 89, 0.3);
  }
`

const projects = [
  {
    id: 1,
    title: "Premium Mango Orchard Development",
    location: "Raichur, Karnataka",
    image: "/darvi-images/field1.png",
    area: "25 Acres",
    investment: "₹1.2 Crores",
    duration: "3 Years",
    status: "ongoing",
    category: "Fruit Farming",
    description: "A comprehensive mango orchard development project featuring premium Alphonso and Kesar varieties with modern irrigation and organic farming practices.",
    progress: 65
  },
  {
    id: 2,
    title: "Organic Vegetable Farm Complex",
    location: "Belgaum, Karnataka",
    image: "/darvi-images/field2.jpg",
    area: "18 Acres",
    investment: "₹85 Lakhs",
    duration: "2 Years",
    status: "completed",
    category: "Organic Farming",
    description: "Certified organic vegetable farming complex with greenhouse facilities and sustainable water management systems for year-round production.",
    progress: 100
  },
  {
    id: 3,
    title: "Smart Greenhouse Technology Hub",
    location: "Hubli, Karnataka",
    image: "/darvi-images/field3.jpg",
    area: "12 Acres",
    investment: "₹2.5 Crores",
    duration: "4 Years",
    status: "upcoming",
    category: "Technology",
    description: "State-of-the-art greenhouse complex with IoT sensors, automated climate control, and hydroponic systems for maximum yield efficiency.",
    progress: 15
  },
  {
    id: 4,
    title: "Sustainable Rice Cultivation",
    location: "Davangere, Karnataka",
    image: "/darvi-images/field4.jpg",
    area: "50 Acres",
    investment: "₹75 Lakhs",
    duration: "2 Years",
    status: "ongoing",
    category: "Grain Farming",
    description: "Large-scale sustainable rice cultivation using SRI (System of Rice Intensification) methods with water-efficient irrigation systems.",
    progress: 40
  },
  {
    id: 5,
    title: "Medicinal Plants Cultivation",
    location: "Chitradurga, Karnataka",
    image: "/darvi-images/field5.jpg",
    area: "8 Acres",
    investment: "₹45 Lakhs",
    duration: "3 Years",
    status: "completed",
    category: "Medicinal",
    description: "Specialized cultivation of high-value medicinal plants including turmeric, ashwagandha, and brahmi with organic certification.",
    progress: 100
  },
  {
    id: 6,
    title: "Integrated Dairy & Fodder Farm",
    location: "Hassan, Karnataka",
    image: "/darvi-images/field6.jpg",
    area: "30 Acres",
    investment: "₹1.8 Crores",
    duration: "5 Years",
    status: "upcoming",
    category: "Integrated Farming",
    description: "Comprehensive integrated farming project combining dairy operations with fodder cultivation and biogas production for sustainability.",
    progress: 5
  }
]

const categories = ['All', 'Fruit Farming', 'Organic Farming', 'Technology', 'Grain Farming', 'Medicinal', 'Integrated Farming']

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
}

const cardVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
}

function Projects() {
  const [activeFilter, setActiveFilter] = useState('All')
  
  const filteredProjects = activeFilter === 'All' 
    ? projects 
    : projects.filter(project => project.category === activeFilter)

  return (
    <ProjectsContainer>
      <HeroSection
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className="container">
          <SectionTitle
            subtitle="Our Portfolio"
            title="Farmland Development Projects"
            description="Explore our comprehensive portfolio of successful farmland development projects across Karnataka, showcasing sustainable agriculture and innovative farming techniques."
          />
        </div>
      </HeroSection>
      
      <CurvedDivider type="wave1" fillColor="white" opacity={1} />
      
      <FilterSection
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <div className="container">
          <FilterContainer>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <FaFilter style={{ color: 'var(--primary-color)', fontSize: '1.2rem' }} />
              <h3 style={{ margin: 0, color: 'var(--primary-dark)' }}>Filter Projects</h3>
            </div>
            
            <FilterButtons>
              {categories.map((category) => (
                <FilterButton
                  key={category}
                  active={activeFilter === category}
                  onClick={() => setActiveFilter(category)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {category}
                </FilterButton>
              ))}
            </FilterButtons>
          </FilterContainer>
        </div>
      </FilterSection>
      
      <section style={{ background: 'linear-gradient(to bottom, white, #f8f9fa)' }}>
        <div className="container">
          <ProjectsGrid
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            key={activeFilter} // Re-animate when filter changes
          >
            {filteredProjects.map((project) => (
              <ProjectCard
                key={project.id}
                variants={cardVariants}
                whileHover={{ scale: 1.02 }}
              >
                <ProjectImage style={{ backgroundImage: `url(${project.image})` }}>
                  <ProjectBadge status={project.status}>
                    {project.status}
                  </ProjectBadge>
                </ProjectImage>
                
                <ProjectContent>
                  <ProjectTitle>{project.title}</ProjectTitle>
                  
                  <ProjectLocation>
                    <FaMapMarkerAlt />
                    {project.location}
                  </ProjectLocation>
                  
                  <ProjectDetails>
                    <DetailItem>
                      <FaRulerCombined />
                      <span className="value">{project.area}</span>
                    </DetailItem>
                    
                    <DetailItem>
                      <FaRupeeSign />
                      <span className="value">{project.investment}</span>
                    </DetailItem>
                    
                    <DetailItem>
                      <FaCalendarAlt />
                      <span className="value">{project.duration}</span>
                    </DetailItem>
                    
                    <DetailItem>
                      <FaLeaf />
                      <span className="value">{project.category}</span>
                    </DetailItem>
                  </ProjectDetails>
                  
                  <ProjectDescription>
                    {project.description}
                  </ProjectDescription>
                  
                  <ProjectButton
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    View Project Details
                  </ProjectButton>
                </ProjectContent>
              </ProjectCard>
            ))}
          </ProjectsGrid>
        </div>
      </section>
      
      <CurvedDivider type="wave2" fillColor="var(--primary-color)" opacity={0.1} />
    </ProjectsContainer>
  )
}

export default Projects