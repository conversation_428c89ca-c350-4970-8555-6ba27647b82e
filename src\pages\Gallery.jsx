import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import styled from 'styled-components'
import { FaTimes, FaChevronLeft, FaChevronRight, FaExpand } from 'react-icons/fa'
import SectionTitle from '../components/SectionTitle'
import CurvedDivider from '../components/CurvedDivider'

const GalleryContainer = styled.div`
  padding-top: 100px;
  min-height: 100vh;
`

const HeroSection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(135deg, #f0f7eb 0%, #e8f5e3 100%);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%234a7c59" fill-opacity="0.1"/><circle cx="80" cy="80" r="2" fill="%234a7c59" fill-opacity="0.1"/></svg>');
    background-size: 100px 100px;
    pointer-events: none;
  }
`

const FilterSection = styled(motion.section)`
  padding: var(--spacing-xl) 0;
  background: white;
`

const FilterButtons = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 3rem;
`

const FilterButton = styled(motion.button)`
  padding: 0.75rem 1.5rem;
  border: 2px solid ${props => props.active ? 'var(--primary-color)' : 'rgba(74, 124, 89, 0.2)'};
  background: ${props => props.active ? 'var(--primary-color)' : 'transparent'};
  color: ${props => props.active ? 'white' : 'var(--primary-color)'};
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
  }
`

const GalleryGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  padding: var(--spacing-xl) 0;
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
`

const GalleryItem = styled(motion.div)`
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 25px rgba(74, 124, 89, 0.1);
  transition: all 0.4s ease;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(74, 124, 89, 0.2);
  }
  
  &:nth-child(3n+1) {
    grid-row: span 2;
  }
  
  &:nth-child(5n+1) {
    grid-column: span 2;
  }
  
  @media (max-width: 768px) {
    &:nth-child(3n+1) {
      grid-row: span 1;
    }
    
    &:nth-child(5n+1) {
      grid-column: span 1;
    }
  }
`

const GalleryImage = styled.div`
  width: 100%;
  height: 250px;
  background-size: cover;
  background-position: center;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.3) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  ${GalleryItem}:hover &::before {
    opacity: 1;
  }
  
  ${GalleryItem}:nth-child(3n+1) & {
    height: 400px;
  }
  
  @media (max-width: 768px) {
    height: 200px;
    
    ${GalleryItem}:nth-child(3n+1) & {
      height: 200px;
    }
  }
`

const GalleryOverlay = styled(motion.div)`
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  
  ${GalleryItem}:hover & {
    opacity: 1;
  }
`

const ExpandIcon = styled(motion.div)`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.5rem;
  
  &:hover {
    background: var(--primary-color);
    color: white;
  }
`

const GalleryInfo = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1.5rem;
  background: linear-gradient(transparent, rgba(0,0,0,0.8));
  color: white;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  
  ${GalleryItem}:hover & {
    transform: translateY(0);
  }
  
  h4 {
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
  }
  
  p {
    font-size: 0.9rem;
    opacity: 0.9;
    margin: 0;
  }
`

const Lightbox = styled(motion.div)`
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 2rem;
`

const LightboxImage = styled(motion.img)`
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  border-radius: 8px;
`

const LightboxClose = styled(motion.button)`
  position: absolute;
  top: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`

const LightboxNav = styled(motion.button)`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
  
  &.prev {
    left: 2rem;
  }
  
  &.next {
    right: 2rem;
  }
`

const galleryImages = [
  {
    id: 1,
    src: "/darvi-images/field1.png",
    title: "Mango Orchard Development",
    description: "Premium mango cultivation with modern irrigation",
    category: "Orchards"
  },
  {
    id: 2,
    src: "/darvi-images/field2.jpg",
    title: "Organic Vegetable Farm",
    description: "Sustainable organic farming practices",
    category: "Organic"
  },
  {
    id: 3,
    src: "/darvi-images/field3.jpg",
    title: "Smart Greenhouse Technology",
    description: "Advanced hydroponic systems",
    category: "Technology"
  },
  {
    id: 4,
    src: "/darvi-images/field4.jpg",
    title: "Rice Cultivation Fields",
    description: "Sustainable rice farming with SRI methods",
    category: "Grains"
  },
  {
    id: 5,
    src: "/darvi-images/field5.jpg",
    title: "Medicinal Plants Garden",
    description: "High-value medicinal plant cultivation",
    category: "Medicinal"
  },
  {
    id: 6,
    src: "/darvi-images/field6.jpg",
    title: "Integrated Dairy Farm",
    description: "Combined dairy and fodder operations",
    category: "Integrated"
  },
  {
    id: 7,
    src: "/darvi-images/field7.jpg",
    title: "Crop Monitoring Systems",
    description: "IoT-enabled crop health monitoring",
    category: "Technology"
  },
  {
    id: 8,
    src: "/darvi-images/hero1.jpg",
    title: "Farmland Overview",
    description: "Aerial view of our managed farmlands",
    category: "Overview"
  },
  {
    id: 9,
    src: "/darvi-images/hero2.jpg",
    title: "Harvest Season",
    description: "Successful harvest from our projects",
    category: "Harvest"
  },
  {
    id: 10,
    src: "/darvi-images/hero3.jpg",
    title: "Farm Equipment",
    description: "Modern agricultural machinery",
    category: "Equipment"
  },
  {
    id: 11,
    src: "/darvi-images/hero4.jpg",
    title: "Irrigation Infrastructure",
    description: "Advanced water management systems",
    category: "Infrastructure"
  },
  {
    id: 12,
    src: "/darvi-images/hero5.jpg",
    title: "Team at Work",
    description: "Our expert agricultural team",
    category: "Team"
  }
]

const categories = ['All', 'Orchards', 'Organic', 'Technology', 'Grains', 'Medicinal', 'Integrated', 'Overview', 'Harvest', 'Equipment', 'Infrastructure', 'Team']

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: "easeOut"
    }
  }
}

function Gallery() {
  const [activeFilter, setActiveFilter] = useState('All')
  const [lightboxImage, setLightboxImage] = useState(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  
  const filteredImages = activeFilter === 'All' 
    ? galleryImages 
    : galleryImages.filter(image => image.category === activeFilter)

  const openLightbox = (image, index) => {
    setLightboxImage(image)
    setCurrentIndex(index)
  }

  const closeLightbox = () => {
    setLightboxImage(null)
  }

  const nextImage = () => {
    const nextIndex = (currentIndex + 1) % filteredImages.length
    setCurrentIndex(nextIndex)
    setLightboxImage(filteredImages[nextIndex])
  }

  const prevImage = () => {
    const prevIndex = (currentIndex - 1 + filteredImages.length) % filteredImages.length
    setCurrentIndex(prevIndex)
    setLightboxImage(filteredImages[prevIndex])
  }

  return (
    <GalleryContainer>
      <HeroSection
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className="container">
          <SectionTitle
            subtitle="Visual Journey"
            title="Farmland Gallery"
            description="Explore our comprehensive gallery showcasing successful farmland projects, modern agricultural techniques, and the beauty of sustainable farming practices."
          />
        </div>
      </HeroSection>
      
      <CurvedDivider type="wave1" fillColor="white" opacity={1} />
      
      <FilterSection
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <div className="container">
          <FilterButtons>
            {categories.map((category) => (
              <FilterButton
                key={category}
                active={activeFilter === category}
                onClick={() => setActiveFilter(category)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {category}
              </FilterButton>
            ))}
          </FilterButtons>
          
          <GalleryGrid
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            key={activeFilter}
          >
            {filteredImages.map((image, index) => (
              <GalleryItem
                key={image.id}
                variants={itemVariants}
                onClick={() => openLightbox(image, index)}
                whileHover={{ scale: 1.02 }}
              >
                <GalleryImage style={{ backgroundImage: `url(${image.src})` }} />
                
                <GalleryOverlay>
                  <ExpandIcon
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <FaExpand />
                  </ExpandIcon>
                </GalleryOverlay>
                
                <GalleryInfo>
                  <h4>{image.title}</h4>
                  <p>{image.description}</p>
                </GalleryInfo>
              </GalleryItem>
            ))}
          </GalleryGrid>
        </div>
      </FilterSection>
      
      <CurvedDivider type="wave2" fillColor="var(--primary-color)" opacity={0.1} />
      
      <AnimatePresence>
        {lightboxImage && (
          <Lightbox
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={closeLightbox}
          >
            <LightboxImage
              src={lightboxImage.src}
              alt={lightboxImage.title}
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.8 }}
              onClick={(e) => e.stopPropagation()}
            />
            
            <LightboxClose
              onClick={closeLightbox}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <FaTimes />
            </LightboxClose>
            
            {filteredImages.length > 1 && (
              <>
                <LightboxNav
                  className="prev"
                  onClick={(e) => {
                    e.stopPropagation()
                    prevImage()
                  }}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FaChevronLeft />
                </LightboxNav>
                
                <LightboxNav
                  className="next"
                  onClick={(e) => {
                    e.stopPropagation()
                    nextImage()
                  }}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <FaChevronRight />
                </LightboxNav>
              </>
            )}
          </Lightbox>
        )}
      </AnimatePresence>
    </GalleryContainer>
  )
}

export default Gallery