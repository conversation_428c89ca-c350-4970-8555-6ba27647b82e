import { useState } from 'react'
import { motion } from 'framer-motion'
import styled from 'styled-components'
import { FaCalendarAlt, FaUser, FaTag, FaArrowRight, FaSearch } from 'react-icons/fa'
import SectionTitle from '../components/SectionTitle'
import CurvedDivider from '../components/CurvedDivider'

const BlogContainer = styled.div`
  padding-top: 100px;
  min-height: 100vh;
`

const HeroSection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(135deg, #f0f7eb 0%, #e8f5e3 100%);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%234a7c59" fill-opacity="0.1"/><circle cx="80" cy="80" r="2" fill="%234a7c59" fill-opacity="0.1"/></svg>');
    background-size: 100px 100px;
    pointer-events: none;
  }
`

const BlogSection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: white;
`

const BlogGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  
  @media (max-width: 968px) {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
`

const BlogMain = styled.div``

const BlogSidebar = styled.div``

const SearchBox = styled.div`
  margin-bottom: 3rem;
  position: relative;
`

const SearchInput = styled.input`
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid rgba(74, 124, 89, 0.2);
  border-radius: 50px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(74, 124, 89, 0.1);
  }
  
  &::placeholder {
    color: var(--text-muted);
  }
`

const SearchIcon = styled.div`
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 1rem;
`

const BlogPost = styled(motion.article)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(74, 124, 89, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin-bottom: 3rem;
  transition: all 0.4s ease;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 30px 60px rgba(74, 124, 89, 0.2);
  }
`

const PostImage = styled.div`
  height: 300px;
  background-size: cover;
  background-position: center;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.1) 100%);
  }
`

const PostCategory = styled.div`
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`

const PostContent = styled.div`
  padding: 2.5rem;
`

const PostMeta = styled.div`
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 1.5rem;
  color: var(--text-muted);
  font-size: 0.9rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
`

const MetaItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  svg {
    color: var(--primary-color);
  }
`

const PostTitle = styled.h2`
  margin-bottom: 1rem;
  color: var(--primary-dark);
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1.8rem;
  line-height: 1.3;
  
  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`

const PostExcerpt = styled.p`
  color: var(--text-muted);
  line-height: 1.7;
  margin-bottom: 2rem;
  font-size: 1.1rem;
`

const ReadMoreButton = styled(motion.button)`
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(74, 124, 89, 0.3);
  }
  
  svg {
    transition: transform 0.3s ease;
  }
  
  &:hover svg {
    transform: translateX(4px);
  }
`

const SidebarWidget = styled(motion.div)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 15px 30px rgba(74, 124, 89, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
`

const WidgetTitle = styled.h3`
  margin-bottom: 1.5rem;
  color: var(--primary-dark);
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1.3rem;
`

const CategoryList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`

const CategoryItem = styled.li`
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(74, 124, 89, 0.1);
  
  &:last-child {
    border-bottom: none;
  }
  
  a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--text-dark);
    text-decoration: none;
    transition: color 0.3s ease;
    
    &:hover {
      color: var(--primary-color);
    }
  }
  
  .count {
    background: rgba(74, 124, 89, 0.1);
    color: var(--primary-color);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
  }
`

const RecentPost = styled.div`
  display: flex;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(74, 124, 89, 0.1);
  
  &:last-child {
    border-bottom: none;
  }
`

const RecentPostImage = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 12px;
  background-size: cover;
  background-position: center;
  flex-shrink: 0;
`

const RecentPostContent = styled.div`
  flex: 1;
  
  h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-dark);
    font-size: 0.95rem;
    font-weight: 600;
    line-height: 1.3;
  }
  
  .date {
    color: var(--text-muted);
    font-size: 0.8rem;
  }
`

const TagCloud = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
`

const Tag = styled.span`
  padding: 0.5rem 1rem;
  background: rgba(74, 124, 89, 0.1);
  color: var(--primary-color);
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
  }
`

const blogPosts = [
  {
    id: 1,
    title: "The Future of Sustainable Agriculture: Technology Meets Tradition",
    excerpt: "Discover how modern technology is revolutionizing traditional farming practices while maintaining environmental sustainability and increasing crop yields.",
    image: "https://images.unsplash.com/photo-1721219314488-cecc1c5f6539?crop=entropy&cs=srgb&fm=jpg&ixid=************************************************************************************************************************************&ixlib=rb-4.1.0&q=85",
    category: "Technology",
    author: "Dr. Rajesh Kumar",
    date: "December 15, 2024",
    tags: ["Technology", "Sustainability", "Innovation"]
  },
  {
    id: 2,
    title: "Maximizing ROI in Farmland Investments: A Complete Guide",
    excerpt: "Learn the essential strategies and factors to consider when investing in farmland to ensure maximum returns and long-term profitability.",
    image: "https://images.unsplash.com/photo-1601125791528-ecf29ca06f6c?crop=entropy&cs=srgb&fm=jpg&ixid=********************************************************************************************************************************************&ixlib=rb-4.1.0&q=85",
    category: "Investment",
    author: "Priya Sharma",
    date: "December 12, 2024",
    tags: ["Investment", "ROI", "Finance"]
  },
  {
    id: 3,
    title: "Organic Farming: Benefits Beyond Profits",
    excerpt: "Explore the environmental and health benefits of organic farming practices and how they contribute to sustainable agriculture and community well-being.",
    image: "https://images.unsplash.com/photo-1708266657628-eff803a7754a?crop=entropy&cs=srgb&fm=jpg&ixid=*******************************************************************************************************************************************&ixlib=rb-4.1.0&q=85",
    category: "Organic",
    author: "Amit Patel",
    date: "December 10, 2024",
    tags: ["Organic", "Health", "Environment"]
  }
]

const categories = [
  { name: "Technology", count: 8 },
  { name: "Investment", count: 12 },
  { name: "Organic", count: 6 },
  { name: "Sustainability", count: 10 },
  { name: "Market Trends", count: 5 },
  { name: "Success Stories", count: 7 }
]

const recentPosts = [
  {
    title: "Smart Irrigation Systems for Water Conservation",
    date: "Dec 14, 2024",
    image: "/darvi-images/field3.jpg"
  },
  {
    title: "Crop Rotation Strategies for Soil Health",
    date: "Dec 13, 2024",
    image: "/darvi-images/field4.jpg"
  },
  {
    title: "Investment Opportunities in Agri-Tech",
    date: "Dec 11, 2024",
    image: "/darvi-images/field5.jpg"
  }
]

const tags = ["Agriculture", "Investment", "Technology", "Organic", "Sustainability", "ROI", "Innovation", "Farming", "Environment", "Finance"]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
}

function Blog() {
  const [searchTerm, setSearchTerm] = useState('')

  return (
    <BlogContainer>
      <HeroSection
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className="container">
          <SectionTitle
            subtitle="Knowledge Hub"
            title="Agricultural Insights & Updates"
            description="Stay informed with the latest trends, technologies, and insights in sustainable agriculture and farmland investment opportunities."
          />
        </div>
      </HeroSection>
      
      <CurvedDivider type="wave1" fillColor="white" opacity={1} />
      
      <BlogSection
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="container">
          <BlogGrid>
            <BlogMain>
              <motion.div
                variants={containerVariants}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
              >
                {blogPosts.map((post) => (
                  <BlogPost
                    key={post.id}
                    variants={itemVariants}
                    whileHover={{ scale: 1.02 }}
                  >
                    <PostImage style={{ backgroundImage: `url(${post.image})` }}>
                      <PostCategory>{post.category}</PostCategory>
                    </PostImage>
                    
                    <PostContent>
                      <PostMeta>
                        <MetaItem>
                          <FaUser />
                          {post.author}
                        </MetaItem>
                        <MetaItem>
                          <FaCalendarAlt />
                          {post.date}
                        </MetaItem>
                        <MetaItem>
                          <FaTag />
                          {post.tags.join(', ')}
                        </MetaItem>
                      </PostMeta>
                      
                      <PostTitle>{post.title}</PostTitle>
                      <PostExcerpt>{post.excerpt}</PostExcerpt>
                      
                      <ReadMoreButton
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        Read More
                        <FaArrowRight />
                      </ReadMoreButton>
                    </PostContent>
                  </BlogPost>
                ))}
              </motion.div>
            </BlogMain>
            
            <BlogSidebar>
              <motion.div
                variants={containerVariants}
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
              >
                <SearchBox>
                  <SearchIcon>
                    <FaSearch />
                  </SearchIcon>
                  <SearchInput
                    type="text"
                    placeholder="Search articles..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </SearchBox>
                
                <SidebarWidget variants={itemVariants}>
                  <WidgetTitle>Categories</WidgetTitle>
                  <CategoryList>
                    {categories.map((category, index) => (
                      <CategoryItem key={index}>
                        <a href="#">
                          <span>{category.name}</span>
                          <span className="count">{category.count}</span>
                        </a>
                      </CategoryItem>
                    ))}
                  </CategoryList>
                </SidebarWidget>
                
                <SidebarWidget variants={itemVariants}>
                  <WidgetTitle>Recent Posts</WidgetTitle>
                  {recentPosts.map((post, index) => (
                    <RecentPost key={index}>
                      <RecentPostImage style={{ backgroundImage: `url(${post.image})` }} />
                      <RecentPostContent>
                        <h4>{post.title}</h4>
                        <div className="date">{post.date}</div>
                      </RecentPostContent>
                    </RecentPost>
                  ))}
                </SidebarWidget>
                
                <SidebarWidget variants={itemVariants}>
                  <WidgetTitle>Popular Tags</WidgetTitle>
                  <TagCloud>
                    {tags.map((tag, index) => (
                      <Tag key={index}>{tag}</Tag>
                    ))}
                  </TagCloud>
                </SidebarWidget>
              </motion.div>
            </BlogSidebar>
          </BlogGrid>
        </div>
      </BlogSection>
      
      <CurvedDivider type="wave2" fillColor="var(--primary-color)" opacity={0.1} />
    </BlogContainer>
  )
}

export default Blog