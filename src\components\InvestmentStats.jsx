import { motion } from 'framer-motion'
import styled from 'styled-components'
import { FaUsers, FaRupeeSign, FaChartLine, FaLeaf } from 'react-icons/fa'
import AnimatedCounter from './AnimatedCounter'
import SectionTitle from './SectionTitle'

const StatsSection = styled(motion.section)`
  background: linear-gradient(135deg, #2e5d35, #4a7c59);
  color: var(--text-light);
  padding: var(--spacing-xxl) 0;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%23ffffff" fill-opacity="0.1"/><circle cx="80" cy="80" r="2" fill="%23ffffff" fill-opacity="0.1"/><circle cx="50" cy="50" r="3" fill="%23ffffff" fill-opacity="0.1"/></svg>');
    background-size: 100px 100px;
    pointer-events: none;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 50%);
    pointer-events: none;
  }
`

const StatsGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2.5rem;
  position: relative;
  z-index: 1;
  margin-top: 3rem;
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
`

const StatCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2.5rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-color), var(--accent-light));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.6s ease;
  }
  
  &:hover {
    transform: translateY(-8px) scale(1.02);
    background: rgba(255, 255, 255, 0.25);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    
    &::before {
      transform: scaleX(1);
    }
  }
  
  @media (max-width: 768px) {
    padding: 2rem;
    
    &:hover {
      transform: translateY(-4px) scale(1.01);
    }
  }
  
  @media (max-width: 480px) {
    padding: 1.5rem;
  }
`

const StatIcon = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  svg {
    font-size: 2rem;
    color: white;
    transition: transform 0.3s ease;
  }
  
  ${StatCard}:hover & {
    svg {
      transform: scale(1.1);
    }
    
    &::after {
      opacity: 1;
    }
  }
  
  @media (max-width: 768px) {
    width: 60px;
    height: 60px;
    
    svg {
      font-size: 1.5rem;
    }
  }
`

const StatNumber = styled(motion.div)`
  font-family: var(--font-heading);
  font-weight: 800;
  font-size: clamp(2.5rem, 5vw, 4rem);
  color: white;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #ffffff, var(--accent-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  
  &::after {
    content: attr(data-suffix);
    position: absolute;
    right: -0.5em;
    top: 0;
    font-size: 0.6em;
    color: var(--accent-light);
    -webkit-text-fill-color: var(--accent-light);
  }
`

const StatLabel = styled.p`
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
`

const StatDescription = styled.p`
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
`

const FloatingElement = styled(motion.div)`
  position: absolute;
  z-index: 0;
  opacity: 0.3;
  pointer-events: none;
  color: white;
`

const stats = [
  {
    icon: <FaUsers />,
    number: "50",
    suffix: "+",
    label: "Happy Investors",
    description: "Satisfied clients who trust our farmland investment expertise"
  },
  {
    icon: <FaRupeeSign />,
    number: "500",
    suffix: "K+", 
    label: "Total Investment",
    description: "Successfully managed investment portfolio value"
  },
  {
    icon: <FaLeaf />,
    number: "200",
    suffix: "",
    label: "Acres Managed",
    description: "Premium farmland under professional management"
  },
  {
    icon: <FaChartLine />,
    number: "12",
    suffix: "%",
    label: "Annual Returns",
    description: "Guaranteed returns on all farmland investments"
  }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.3
    }
  }
}

const cardVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut"
    }
  }
}

function InvestmentStats() {
  return (
    <StatsSection
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
    >
      {/* Floating decorative elements */}
      <FloatingElement
        style={{ top: '15%', left: '5%' }}
        animate={{
          y: [0, -20, 0],
          rotate: [0, 10, 0]
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <FaLeaf size={60} />
      </FloatingElement>
      
      <FloatingElement
        style={{ top: '70%', right: '8%' }}
        animate={{
          y: [0, -15, 0],
          rotate: [0, -8, 0]
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 3
        }}
      >
        <FaChartLine size={50} />
      </FloatingElement>

      <div className="container">
        <SectionTitle
          subtitle="Our Success"
          title="Invest in Green Growth, Harvest Real Profits"
          description="Join hundreds of successful investors who have already benefited from our proven farmland investment strategies and guaranteed returns."
          style={{ color: 'var(--text-light)' }}
        />
        
        <StatsGrid
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
        >
          {stats.map((stat, index) => (
            <StatCard
              key={index}
              variants={cardVariants}
              whileHover={{ scale: 1.05 }}
            >
              <StatIcon>
                {stat.icon}
              </StatIcon>
              
              <AnimatedCounter
                end={stat.number + stat.suffix}
                label=""
                duration={2000}
                delay={index * 200}
              />
              
              <StatLabel>{stat.label}</StatLabel>
              <StatDescription>{stat.description}</StatDescription>
            </StatCard>
          ))}
        </StatsGrid>
      </div>
    </StatsSection>
  )
}

export default InvestmentStats