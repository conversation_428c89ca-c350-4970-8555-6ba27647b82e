[build]
  command = "npm install && node check-directories.js && npm run build"
  publish = "dist"

# Redirect all requests to index.html for SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Proxy requests to Netlify CMS
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

# Enable Netlify Identity
[template.environment]
  NETLIFY_IDENTITY_ENABLED = "true"
