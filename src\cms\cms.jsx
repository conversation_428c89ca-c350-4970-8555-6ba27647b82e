import React from 'react';
import CMS from 'decap-cms-app';
import { StyleSheetManager } from 'styled-components';

// Preview templates
import HomePagePreview from './preview-templates/HomePagePreview';
import AboutPagePreview from './preview-templates/AboutPagePreview';
import ApproachPagePreview from './preview-templates/ApproachPagePreview';
import FarmsPagePreview from './preview-templates/FarmsPagePreview';
import InvestmentPagePreview from './preview-templates/InvestmentPagePreview';
import TechnologyPagePreview from './preview-templates/TechnologyPagePreview';
import SustainabilityPagePreview from './preview-templates/SustainabilityPagePreview';
import ResourcesPagePreview from './preview-templates/ResourcesPagePreview';
import ContactPagePreview from './preview-templates/ContactPagePreview';

// Register the CMS
CMS.init();

// Register preview templates
CMS.registerPreviewTemplate('home', HomePagePreview);
CMS.registerPreviewTemplate('about', AboutPagePreview);
CMS.registerPreviewTemplate('approach', ApproachPagePreview);
CMS.registerPreviewTemplate('farms', FarmsPagePreview);
CMS.registerPreviewTemplate('investment', InvestmentPagePreview);
CMS.registerPreviewTemplate('technology', TechnologyPagePreview);
CMS.registerPreviewTemplate('sustainability', SustainabilityPagePreview);
CMS.registerPreviewTemplate('resources', ResourcesPagePreview);
CMS.registerPreviewTemplate('contact', ContactPagePreview);

// Custom widget for styled components
const StyleSheetInjector = ({ children }) => {
  const iframe = document.querySelector('#nc-root iframe');
  const iframeHeadElem = iframe && iframe.contentDocument.head;

  if (!iframeHeadElem) {
    return null;
  }

  return (
    <StyleSheetManager target={iframeHeadElem}>
      {children}
    </StyleSheetManager>
  );
};

export { StyleSheetInjector };
