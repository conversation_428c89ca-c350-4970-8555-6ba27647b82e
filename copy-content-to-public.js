/**
 * Script to copy content files from src/content to public/content for local development
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Source and destination directories
const sourceDir = path.join(__dirname, 'src', 'content');
const destDir = path.join(__dirname, 'public', 'content');

// Create the destination directory if it doesn't exist
if (!fs.existsSync(destDir)) {
  fs.mkdirSync(destDir, { recursive: true });
  console.log(`Created directory: ${destDir}`);
}

// Copy all files from the source directory to the destination directory
const copyFiles = (src, dest) => {
  const files = fs.readdirSync(src);
  
  files.forEach(file => {
    const srcPath = path.join(src, file);
    const destPath = path.join(dest, file);
    
    const stat = fs.statSync(srcPath);
    
    if (stat.isDirectory()) {
      // If it's a directory, create it in the destination and copy its contents
      if (!fs.existsSync(destPath)) {
        fs.mkdirSync(destPath, { recursive: true });
      }
      copyFiles(srcPath, destPath);
    } else {
      // If it's a file, copy it to the destination
      fs.copyFileSync(srcPath, destPath);
      console.log(`Copied: ${srcPath} -> ${destPath}`);
    }
  });
};

try {
  // Check if the source directory exists
  if (fs.existsSync(sourceDir)) {
    copyFiles(sourceDir, destDir);
    console.log('Content files copied to public directory successfully!');
  } else {
    console.log(`Source directory ${sourceDir} does not exist.`);
  }
} catch (error) {
  console.error('Error copying content files:', error);
}
