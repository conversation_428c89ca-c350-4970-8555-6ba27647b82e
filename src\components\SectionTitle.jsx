import { motion } from 'framer-motion'
import styled from 'styled-components'

const TitleContainer = styled.div`
  text-align: center;
  margin-bottom: 3rem;

  @media (max-width: 768px) {
    margin-bottom: 2rem;
  }

  @media (max-width: 480px) {
    margin-bottom: 1.5rem;
  }
`

const Subtitle = styled(motion.p)`
  color: var(--primary-color);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 0.5rem;

  @media (max-width: 768px) {
    font-size: 0.9rem;
    letter-spacing: 1px;
  }

  @media (max-width: 480px) {
    font-size: 0.8rem;
    letter-spacing: 0.5px;
  }
`

const Title = styled(motion.h2)`
  position: relative;
  display: inline-block;
  margin-bottom: 1.5rem;

  &:after {
    content: '';
    position: absolute;
    bottom: -0.75rem;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--primary-color);
  }

  @media (max-width: 768px) {
    font-size: 2rem;
    margin-bottom: 1rem;

    &:after {
      width: 60px;
      height: 2px;
    }
  }

  @media (max-width: 480px) {
    font-size: 1.5rem;

    &:after {
      width: 50px;
    }
  }
`

const Description = styled(motion.p)`
  max-width: 700px;
  margin: 0 auto;
  color: var(--text-dark);
  opacity: 0.8;
`

function SectionTitle({ subtitle, title, description, style = {} }) {
  return (
    <TitleContainer style={style}>
      {subtitle && (
        <Subtitle
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          style={style}
        >
          {subtitle}
        </Subtitle>
      )}

      <Title
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5, delay: 0.1 }}
        style={style}
      >
        {title}
      </Title>

      {description && (
        <Description
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
          style={style}
        >
          {description}
        </Description>
      )}
    </TitleContainer>
  )
}

export default SectionTitle
