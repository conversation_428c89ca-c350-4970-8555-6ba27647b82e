import { motion } from 'framer-motion'
import styled from 'styled-components'
import { FaMapMarkerAlt, FaRulerCombined, FaRupeeSign, FaArrowRight } from 'react-icons/fa'
import SectionTitle from './SectionTitle'

const PropertySection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(135deg, #f0f7eb 0%, #e8f5e3 100%);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 80 80"><rect fill="%234a7c59" fill-opacity="0.05" width="40" height="40" x="0" y="0"></rect><rect fill="%234a7c59" fill-opacity="0.05" width="40" height="40" x="40" y="40"></rect></svg>');
    background-size: 80px 80px;
    pointer-events: none;
  }
`

const PropertiesGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2.5rem;
  position: relative;
  z-index: 1;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`

const PropertyCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(74, 124, 89, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s ease;
  position: relative;
  
  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 60px rgba(74, 124, 89, 0.2);
  }
  
  @media (max-width: 768px) {
    &:hover {
      transform: translateY(-5px);
    }
  }
`

const PropertyImage = styled.div`
  height: 280px;
  background-size: cover;
  background-position: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.1) 100%);
  }
  
  &::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, transparent 0%, rgba(74, 124, 89, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  ${PropertyCard}:hover &::after {
    opacity: 1;
  }
  
  @media (max-width: 768px) {
    height: 240px;
  }
`

const PropertyBadge = styled.div`
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(74, 124, 89, 0.3);
`

const PropertyContent = styled.div`
  padding: 2rem;
  
  @media (max-width: 768px) {
    padding: 1.5rem;
  }
`

const PropertyTitle = styled.h3`
  margin-bottom: 1rem;
  color: var(--primary-dark);
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1.5rem;
  line-height: 1.3;
  
  @media (max-width: 768px) {
    font-size: 1.3rem;
  }
`

const PropertyLocation = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  color: var(--text-muted);
  font-size: 1rem;
  
  svg {
    margin-right: 0.5rem;
    color: var(--primary-color);
  }
`

const PropertyDetails = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
`

const DetailItem = styled.div`
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background: rgba(74, 124, 89, 0.05);
  border-radius: 12px;
  font-size: 0.95rem;
  
  svg {
    margin-right: 0.75rem;
    color: var(--primary-color);
    font-size: 1.1rem;
  }
  
  .label {
    color: var(--text-muted);
    font-size: 0.85rem;
    display: block;
  }
  
  .value {
    color: var(--text-dark);
    font-weight: 600;
    display: block;
  }
`

const PropertyPrice = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  
  .price {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--primary-color);
    font-family: var(--font-heading);
  }
  
  .period {
    color: var(--text-muted);
    font-size: 0.9rem;
  }
  
  @media (max-width: 768px) {
    .price {
      font-size: 1.5rem;
    }
  }
`

const PropertyButton = styled(motion.button)`
  width: 100%;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(74, 124, 89, 0.3);
  }
  
  svg {
    transition: transform 0.3s ease;
  }
  
  &:hover svg {
    transform: translateX(4px);
  }
`

const properties = [
  {
    id: 1,
    title: "Premium Mango Orchard",
    location: "Raichur, Karnataka",
    image: "https://images.unsplash.com/photo-1708921371195-10e57932622b?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw2fHxmYXJtbGFuZCUyMGNyb3BzJTIwYWdyaWN1bHR1cmUlMjBncmVlbiUyMGZpZWxkc3xlbnwwfDB8fGdyZWVufDE3NTQ0MTY3MTh8MA&ixlib=rb-4.1.0&q=85",
    area: "2.5 Acres",
    investment: "₹12,50,000",
    returns: "12% Annual",
    status: "Available",
    badge: "Premium"
  },
  {
    id: 2,
    title: "Organic Vegetable Farm",
    location: "Belgaum, Karnataka", 
    image: "https://images.unsplash.com/photo-1549024449-d6968d2a435f?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw4fHxmYXJtbGFuZCUyMGNyb3BzJTIwYWdyaWN1bHR1cmUlMjBncmVlbiUyMGZpZWxkc3xlbnwwfDB8fGdyZWVufDE3NTQ0MTY3MTh8MA&ixlib=rb-4.1.0&q=85",
    area: "1.8 Acres",
    investment: "₹9,00,000",
    returns: "12% Annual",
    status: "Available",
    badge: "Organic"
  },
  {
    id: 3,
    title: "Modern Greenhouse Complex",
    location: "Hubli, Karnataka",
    image: "https://images.unsplash.com/photo-1506180064210-cfa64ed82c11?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjBmYXJtJTIwaXJyaWdhdGlvbiUyMGFncmljdWx0dXJlJTIwdGVjaG5vbG9neXxlbnwwfDB8fGdyZWVufDE3NTQ0MTY3MTd8MA&ixlib=rb-4.1.0&q=85",
    area: "3.2 Acres",
    investment: "₹16,00,000",
    returns: "12% Annual", 
    status: "Limited",
    badge: "High-Tech"
  }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
}

const cardVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut"
    }
  }
}

function PropertyShowcase() {
  return (
    <PropertySection
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
    >
      <div className="container">
        <SectionTitle
          subtitle="Featured Properties"
          title="The Perfect Piece of Farmland for You and Your Family"
          description="Explore our carefully selected farmland properties that offer excellent investment opportunities with guaranteed returns and professional management."
        />
        
        <PropertiesGrid
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
        >
          {properties.map((property) => (
            <PropertyCard
              key={property.id}
              variants={cardVariants}
              whileHover={{ scale: 1.02 }}
            >
              <PropertyImage style={{ backgroundImage: `url(${property.image})` }}>
                <PropertyBadge>{property.badge}</PropertyBadge>
              </PropertyImage>
              
              <PropertyContent>
                <PropertyTitle>{property.title}</PropertyTitle>
                
                <PropertyLocation>
                  <FaMapMarkerAlt />
                  {property.location}
                </PropertyLocation>
                
                <PropertyDetails>
                  <DetailItem>
                    <FaRulerCombined />
                    <div>
                      <span className="label">Area</span>
                      <span className="value">{property.area}</span>
                    </div>
                  </DetailItem>
                  
                  <DetailItem>
                    <FaRupeeSign />
                    <div>
                      <span className="label">Returns</span>
                      <span className="value">{property.returns}</span>
                    </div>
                  </DetailItem>
                </PropertyDetails>
                
                <PropertyPrice>
                  <div>
                    <div className="price">{property.investment}</div>
                    <div className="period">Total Investment</div>
                  </div>
                  <div style={{ 
                    padding: '0.5rem 1rem', 
                    background: property.status === 'Available' ? 'rgba(76, 175, 80, 0.1)' : 'rgba(255, 152, 0, 0.1)',
                    color: property.status === 'Available' ? '#4caf50' : '#ff9800',
                    borderRadius: '20px',
                    fontSize: '0.9rem',
                    fontWeight: '600'
                  }}>
                    {property.status}
                  </div>
                </PropertyPrice>
                
                <PropertyButton
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  View Details
                  <FaArrowRight />
                </PropertyButton>
              </PropertyContent>
            </PropertyCard>
          ))}
        </PropertiesGrid>
      </div>
    </PropertySection>
  )
}

export default PropertyShowcase