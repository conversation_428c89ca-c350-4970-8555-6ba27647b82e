/**
 * Simple script to copy the content directory to the dist folder
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Source and destination directories
const sourceDir = path.join(__dirname, 'src', 'content');
const destDir = path.join(__dirname, 'dist', 'content');

// Also copy to the public/content directory for local development
const publicDir = path.join(__dirname, 'public', 'content');

// Create the destination directory if it doesn't exist
if (!fs.existsSync(destDir)) {
  fs.mkdirSync(destDir, { recursive: true });
  console.log(`Created directory: ${destDir}`);
}

// Copy all files from the source directory to the destination directory
const copyFiles = (src, dest) => {
  const files = fs.readdirSync(src);

  files.forEach(file => {
    const srcPath = path.join(src, file);
    const destPath = path.join(dest, file);

    const stat = fs.statSync(srcPath);

    if (stat.isDirectory()) {
      // If it's a directory, create it in the destination and copy its contents
      if (!fs.existsSync(destPath)) {
        fs.mkdirSync(destPath, { recursive: true });
      }
      copyFiles(srcPath, destPath);
    } else {
      // If it's a file, copy it to the destination
      fs.copyFileSync(srcPath, destPath);
      console.log(`Copied: ${srcPath} -> ${destPath}`);
    }
  });
};

// Create the public content directory if it doesn't exist
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
  console.log(`Created directory: ${publicDir}`);
}

try {
  // Check if the source directory exists
  if (fs.existsSync(sourceDir)) {
    // Copy to dist directory
    copyFiles(sourceDir, destDir);
    console.log('Content directory copied to dist successfully!');

    // Also copy to public directory for local development
    copyFiles(sourceDir, publicDir);
    console.log('Content directory copied to public successfully!');
  } else {
    console.log(`Source directory ${sourceDir} does not exist. Creating sample content files.`);

    // Create sample content files
    const sampleContent = {
      home: {
        "heroTitle": "Premium Nursery Plants & Agricultural Solutions",
        "heroSubtitle": "Darvi Group is your trusted partner for high-quality nursery plants and innovative agricultural solutions since 2018.",
        "primaryBtnText": "Explore Our Products",
        "primaryBtnLink": "/farms",
        "secondaryBtnText": "Contact Us",
        "secondaryBtnLink": "/contact"
      },
      about: {
        "heroTitle": "About Darvi Group",
        "heroSubtitle": "Your trusted partner in premium nursery plants and agricultural solutions since 2018.",
        "primaryBtnText": "Meet Our Team",
        "primaryBtnLink": "#team"
      },
      contact: {
        "heroTitle": "Contact Us",
        "heroSubtitle": "Get in touch with our team to discuss your agricultural needs or investment opportunities.",
        "contactInfo": {
          "address": "123 Agriculture Road, Hubli, Karnataka 580030, India",
          "phone": "+91 99868 90777",
          "email": "<EMAIL>"
        }
      }
    };

    // Write sample content files to both directories
    Object.entries(sampleContent).forEach(([page, content]) => {
      const distFilePath = path.join(destDir, `${page}.json`);
      const publicFilePath = path.join(publicDir, `${page}.json`);

      fs.writeFileSync(distFilePath, JSON.stringify(content, null, 2));
      console.log(`Created sample content file: ${distFilePath}`);

      fs.writeFileSync(publicFilePath, JSON.stringify(content, null, 2));
      console.log(`Created sample content file: ${publicFilePath}`);
    });
  }
} catch (error) {
  console.error('Error copying content directory:', error);
  // Don't exit with error code, just log the error
  console.log('Continuing build process despite content copy error...');
}
