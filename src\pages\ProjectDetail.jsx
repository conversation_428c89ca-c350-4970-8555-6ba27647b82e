import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { FaArrowLeft, FaMapMarkerAlt, FaCalendarAlt, FaLeaf, FaUsers, FaChartLine } from 'react-icons/fa'
import styled from 'styled-components'
import HeroSection from '../components/HeroSection'
import SectionTitle from '../components/SectionTitle'
import InteractiveMap from '../components/InteractiveMap'

const ProjectDetailContent = styled.section`
  padding: var(--spacing-xxl) 0;
`

const BackButton = styled(Link)`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 2rem;
  transition: all 0.3s ease;

  &:hover {
    color: var(--primary-dark);
    transform: translateX(-5px);
  }
`

const ProjectInfo = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;

  @media (max-width: 992px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  @media (max-width: 768px) {
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
`

const ProjectDetails = styled.div`
  background: var(--background-light);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);

  @media (max-width: 768px) {
    padding: 1.5rem;
  }

  @media (max-width: 480px) {
    padding: 1rem;
  }
`

const ProjectStats = styled.div`
  background: var(--primary-color);
  color: var(--text-light);
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);

  @media (max-width: 768px) {
    padding: 1.5rem;
  }

  @media (max-width: 480px) {
    padding: 1rem;
  }
`

const StatItem = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;

  &:last-child {
    margin-bottom: 0;
  }

  .icon {
    font-size: 1.5rem;
    color: var(--accent-color);
  }

  .content {
    flex: 1;
  }

  .label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.25rem;
  }

  .value {
    font-size: 1.1rem;
    font-weight: 600;
  }
`

const MapSection = styled.section`
  padding: var(--spacing-xl) 0;
  background: var(--background-cream);
`

const MapContainer = styled.div`
  height: 400px;
  background-color: #e0e0e0;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2rem;
  position: relative;
  overflow: hidden;
`

const MapPlaceholder = styled.div`
  text-align: center;
  z-index: 2;
`

const MapOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  opacity: 0.1;
  z-index: 1;
`

const FeaturesList = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
`

const FeatureCard = styled.div`
  background: var(--background-light);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  border-left: 4px solid var(--primary-color);

  .icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
  }

  h4 {
    color: var(--text-dark);
    margin-bottom: 0.5rem;
  }

  p {
    color: var(--text-muted);
    font-size: 0.9rem;
  }
`

function ProjectDetail() {
  const { id } = useParams()

  // Sample project data - in a real app, this would come from an API
  const projectsData = {
    1: {
      id: 1,
      title: 'Green Valley Farmland Development',
      description: 'A comprehensive 150-acre sustainable farmland development project featuring organic crop cultivation, modern irrigation systems, and eco-friendly infrastructure designed to maximize agricultural productivity while preserving environmental sustainability.',
      status: 'Ongoing',
      location: 'Bangalore Rural, Karnataka, India',
      area: '150 acres',
      type: 'Mixed Agriculture',
      startDate: 'January 2024',
      expectedCompletion: 'December 2025',
      investment: '₹2.5 Crores',
      features: [
        {
          icon: FaLeaf,
          title: 'Organic Farming',
          description: 'Certified organic farming practices with natural fertilizers and pest management'
        },
        {
          icon: FaUsers,
          title: 'Community Impact',
          description: 'Creating employment for 50+ local farmers and agricultural workers'
        },
        {
          icon: FaChartLine,
          title: 'High ROI',
          description: 'Projected 15-20% annual returns through diversified crop cultivation'
        }
      ]
    },
    2: {
      id: 2,
      title: 'Sunrise Agricultural Estate',
      description: 'Premium 200-acre agricultural estate with integrated farming systems and eco-friendly infrastructure, focusing on sustainable agriculture and modern farming techniques.',
      status: 'Ongoing',
      location: 'Salem, Tamil Nadu, India',
      area: '200 acres',
      type: 'Integrated Farming',
      startDate: 'March 2024',
      expectedCompletion: 'February 2026',
      investment: '₹3.2 Crores',
      features: [
        {
          icon: FaLeaf,
          title: 'Integrated Systems',
          description: 'Combined crop and livestock farming for maximum efficiency'
        },
        {
          icon: FaUsers,
          title: 'Training Center',
          description: 'On-site farmer training and education facility'
        },
        {
          icon: FaChartLine,
          title: 'Premium Returns',
          description: 'Expected 18-22% annual returns through diversified agriculture'
        }
      ]
    },
    3: {
      id: 3,
      title: 'Blue River Agro Park',
      description: 'Modern 300-acre agro park with advanced technology integration and sustainable farming practices, featuring precision agriculture and smart irrigation systems.',
      status: 'Upcoming',
      location: 'Kurnool, Andhra Pradesh, India',
      area: '300 acres',
      type: 'Technology Farming',
      startDate: 'August 2024',
      expectedCompletion: 'July 2026',
      investment: '₹4.5 Crores',
      features: [
        {
          icon: FaLeaf,
          title: 'Smart Technology',
          description: 'IoT sensors and automated irrigation systems'
        },
        {
          icon: FaUsers,
          title: 'Research Hub',
          description: 'Agricultural research and development center'
        },
        {
          icon: FaChartLine,
          title: 'Innovation Focus',
          description: 'Cutting-edge farming techniques and crop optimization'
        }
      ]
    },
    4: {
      id: 4,
      title: 'Highland Organic Farms',
      description: 'Certified organic farmland spanning 180 acres with focus on premium crop production and sustainable farming practices in the scenic highlands of Kerala.',
      status: 'Completed',
      location: 'Palakkad, Kerala, India',
      area: '180 acres',
      type: 'Organic Farming',
      startDate: 'January 2022',
      expectedCompletion: 'Completed December 2023',
      investment: '₹2.8 Crores',
      features: [
        {
          icon: FaLeaf,
          title: 'Certified Organic',
          description: 'Fully certified organic farming with premium crop yields'
        },
        {
          icon: FaUsers,
          title: 'Local Employment',
          description: 'Created 75+ permanent jobs for local community'
        },
        {
          icon: FaChartLine,
          title: 'Proven Success',
          description: 'Achieved 20% annual returns with premium organic produce'
        }
      ]
    }
  }

  const project = projectsData[parseInt(id)] || projectsData[1]

  return (
    <>
      <HeroSection
        title={project.title}
        subtitle={`${project.area} • ${project.location} • ${project.status}`}
        primaryBtnText="View Location"
        primaryBtnLink="#map"
      />

      <ProjectDetailContent>
        <div className="container">
          <BackButton to="/projects">
            <FaArrowLeft /> Back to Projects
          </BackButton>

          <ProjectInfo>
            <ProjectDetails>
              <h2>Project Overview</h2>
              <p>{project.description}</p>

              <FeaturesList>
                {project.features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <FeatureCard>
                      <feature.icon className="icon" />
                      <h4>{feature.title}</h4>
                      <p>{feature.description}</p>
                    </FeatureCard>
                  </motion.div>
                ))}
              </FeaturesList>
            </ProjectDetails>

            <ProjectStats>
              <h3>Project Details</h3>
              
              <StatItem>
                <FaMapMarkerAlt className="icon" />
                <div className="content">
                  <div className="label">Location</div>
                  <div className="value">{project.location}</div>
                </div>
              </StatItem>

              <StatItem>
                <FaLeaf className="icon" />
                <div className="content">
                  <div className="label">Total Area</div>
                  <div className="value">{project.area}</div>
                </div>
              </StatItem>

              <StatItem>
                <FaCalendarAlt className="icon" />
                <div className="content">
                  <div className="label">Start Date</div>
                  <div className="value">{project.startDate}</div>
                </div>
              </StatItem>

              <StatItem>
                <FaCalendarAlt className="icon" />
                <div className="content">
                  <div className="label">Expected Completion</div>
                  <div className="value">{project.expectedCompletion}</div>
                </div>
              </StatItem>

              <StatItem>
                <FaChartLine className="icon" />
                <div className="content">
                  <div className="label">Investment</div>
                  <div className="value">{project.investment}</div>
                </div>
              </StatItem>
            </ProjectStats>
          </ProjectInfo>
        </div>
      </ProjectDetailContent>

      <MapSection id="map">
        <div className="container">
          <SectionTitle
            subtitle="Project Location"
            title="Interactive Project Map"
            description="Explore the exact location and surrounding area of this farmland development project."
          />

          <InteractiveMap selectedProject={project.id} height={400} />
        </div>
      </MapSection>
    </>
  )
}

export default ProjectDetail
