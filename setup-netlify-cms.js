/**
 * This script helps set up Decap CMS (formerly Netlify CMS) for your project
 * Run it with: node setup-netlify-cms.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Ensure the public/admin directory exists
const adminDir = path.join(__dirname, 'public', 'admin');
if (!fs.existsSync(adminDir)) {
  fs.mkdirSync(adminDir, { recursive: true });
  console.log('Created public/admin directory');
}

// Ensure the public/images/uploads directory exists
const uploadsDir = path.join(__dirname, 'public', 'images', 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log('Created public/images/uploads directory');
}

// Ensure the src/content directory exists
const contentDir = path.join(__dirname, 'src', 'content');
if (!fs.existsSync(contentDir)) {
  fs.mkdirSync(contentDir, { recursive: true });
  console.log('Created src/content directory');
}

// Check if the Decap CMS config file exists
const configPath = path.join(adminDir, 'config.yml');
if (!fs.existsSync(configPath)) {
  console.log('Decap CMS config file not found. Please create it manually.');
} else {
  console.log('Decap CMS config file exists');
}

// Check if the Decap CMS admin index.html file exists
const indexPath = path.join(adminDir, 'index.html');
if (!fs.existsSync(indexPath)) {
  console.log('Decap CMS admin index.html file not found. Please create it manually.');
} else {
  console.log('Decap CMS admin index.html file exists');
}

// Check if the netlify.toml file exists
const netlifyTomlPath = path.join(__dirname, 'netlify.toml');
if (!fs.existsSync(netlifyTomlPath)) {
  console.log('netlify.toml file not found. Please create it manually.');
} else {
  console.log('netlify.toml file exists');
}

console.log('\nSetup complete! Next steps:');
console.log('1. Deploy your site to Netlify');
console.log('2. Enable Netlify Identity in the Netlify dashboard');
console.log('3. Enable Git Gateway in the Netlify dashboard');
console.log('4. Invite users to your CMS');
console.log('\nFor more information, see the CMS_INSTRUCTIONS.md file.');
