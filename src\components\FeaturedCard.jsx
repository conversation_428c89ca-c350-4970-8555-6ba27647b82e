import { Link } from 'react-router-dom'
import { motion } from 'framer-motion'
import styled from 'styled-components'

const Card = styled(motion.div)`
  background-color: #fff;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
`

const CardImage = styled.div`
  height: 200px;
  background-size: cover;
  background-position: center;
`

const CardContent = styled.div`
  padding: 1.5rem;

  @media (max-width: 768px) {
    padding: 1.25rem;
  }

  @media (max-width: 480px) {
    padding: 1rem;
  }
`

const CardTitle = styled.h3`
  margin-bottom: 0.75rem;
  color: var(--primary-dark);

  @media (max-width: 768px) {
    font-size: 1.3rem;
  }

  @media (max-width: 480px) {
    font-size: 1.2rem;
  }
`

const CardDescription = styled.p`
  color: var(--text-dark);
  margin-bottom: 1.5rem;
  opacity: 0.8;
`

const CardLink = styled(Link)`
  display: inline-flex;
  align-items: center;
  color: var(--primary-color);
  font-weight: 600;

  &:after {
    content: '→';
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
  }

  &:hover:after {
    transform: translateX(5px);
  }
`

const StatusBadge = styled.span`
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 0.75rem;

  &.upcoming {
    background-color: var(--warning-color);
    color: var(--text-dark);
  }

  &.managed {
    background-color: var(--primary-color);
    color: var(--text-light);
  }

  &.completed {
    background-color: var(--success-color);
    color: var(--text-light);
  }
`

function FeaturedCard({ image, title, description, link, status }) {
  const getStatusClass = () => {
    switch(status?.toLowerCase()) {
      case 'upcoming':
        return 'upcoming';
      case 'managed':
        return 'managed';
      case 'completed':
        return 'completed';
      default:
        return '';
    }
  };

  return (
    <Card
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
    >
      <CardImage style={{ backgroundImage: `url(${image || 'https://images.unsplash.com/photo-1464226184884-fa280b87c399?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80'})` }} />
      <CardContent>
        {status && (
          <StatusBadge className={getStatusClass()}>
            {status}
          </StatusBadge>
        )}
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
        <CardLink to={link}>Learn More</CardLink>
      </CardContent>
    </Card>
  )
}

export default FeaturedCard
