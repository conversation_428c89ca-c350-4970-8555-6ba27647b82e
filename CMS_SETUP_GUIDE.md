# Netlify CMS Setup Guide for Darvi Farmlands Website

## Overview
This guide explains how to use the Netlify CMS to manage all content, images, and components on the Darvi Farmlands website. The CMS is fully configured with appropriate headers, hints, and image management.

## 🚀 Accessing the CMS

### Local Development
1. Start the development server: `npm run dev`
2. Open your browser and go to: `http://localhost:5174/admin/`
3. You'll see the CMS login screen

### Production
1. Go to: `https://your-domain.com/admin/`
2. Login with your Netlify account credentials

## 📋 CMS Structure Overview

The CMS is organized into the following main sections:

### 🏠 **Home Page**
- **Hero Section**: Main banner with title, subtitle, background image, and buttons
- **🌱 Featured Projects**: Showcase of farm projects with images and status
- **🛠️ Services**: Agricultural services with icons and descriptions
- **ℹ️ About Section**: Company information and call-to-action
- **📊 Statistics**: Animated counters with agricultural achievements
- **📢 Call-to-Action**: Final conversion section with background image

### 👥 **About Page**
- **Hero Section**: Page introduction
- **📖 Story Section**: Company history and mission
- **📝 Content Blocks**: Flexible content sections (text-focused, images optional)
- **👨‍👩‍👧‍👦 Team Section**: Team member profiles with photos
- **💎 Values Section**: Company values and principles

### 🎯 **Approach Page**
- **Hero Section**: Page introduction
- **🔬 Methods Section**: Farming methods and techniques
- **📝 Main Content**: Detailed approach content (markdown supported)

### 🚜 **Farms Page**
- **Hero Section**: Page introduction
- **🌾 Farm Projects**: Detailed farm project listings with galleries

### 💰 **Investment Page**
- **Hero Section**: Page introduction
- **📈 Investment Options**: Investment opportunities with financial details

### 🔬 **Technology Page**
- **Hero Section**: Page introduction
- **⚙️ Technologies**: Agricultural technologies and innovations

### 🌱 **Sustainability Page**
- **Hero Section**: Page introduction
- **♻️ Sustainability Initiatives**: Environmental practices and initiatives

### 📚 **Resources Page**
- **Hero Section**: Page introduction
- **📖 Resources Section**: Section configuration
- **📄 Resources**: Educational materials and guides

### 📞 **Contact Page**
- **Hero Section**: Page introduction
- **📋 Contact Information**: Business details and hours
- **🗺️ Map Location**: Geographic coordinates for map display

### ⚙️ **Site Settings**
- **🏢 Site Information**: Global site details and branding
- **🧭 Navigation Menu**: Main navigation management
- **📱 Social Media Links**: Social media profiles
- **🔍 SEO Settings**: Search engine optimization

## 🖼️ Image Management

### Image Upload Process
1. **Automatic Handling**: All image fields are configured to upload to `/public/images/uploads/`
2. **Recommended Sizes**:
   - Hero Images: 1920x1080px
   - Project Images: 800x600px
   - Team Photos: 400x400px
   - Resource Thumbnails: 400x300px
   - Investment Images: 600x400px
   - Technology Images: 600x400px

### Image Placeholders
- All image fields have proper fallbacks
- Images are automatically processed and optimized
- Broken image links are handled gracefully

### Current Available Images
- `hero1.jpg` - Main hero background
- `fb_img_1743089684462.jpg` - Sample image
- `img-20250428-wa0071.jpg` - Sample image

## 🎨 Agricultural Icons Available

The following icons are available for services, methods, and other components:

### Primary Agricultural Icons
- `FaLeaf` - General agriculture/plants
- `FaSeedling` - Growth/planting
- `FaTractor` - Farming equipment
- `FaTree` - Forestry/trees
- `FaWater` - Irrigation/water management
- `FaRecycle` - Sustainability

### Technology & Business Icons
- `FaMicrochip` - Smart farming/IoT
- `FaChartLine` - Analytics/growth
- `FaCog` - Technology/systems
- `FaLightbulb` - Innovation
- `FaIndustry` - Industrial agriculture

### Service & Quality Icons
- `FaUsers` - Team/community
- `FaAward` - Quality/achievements
- `FaHandshake` - Partnership
- `FaShieldAlt` - Security/reliability
- `FaGlobe` - Global reach

## 📝 Content Guidelines

### Writing Tips
1. **Headlines**: Keep concise and action-oriented
2. **Descriptions**: Use clear, benefit-focused language
3. **Markdown Support**: Available in description fields for formatting
4. **SEO**: Include relevant keywords naturally

### Image Guidelines
1. **Quality**: Use high-resolution, professional images
2. **Relevance**: Ensure images match the content context
3. **Consistency**: Maintain similar style and quality across all images
4. **Optimization**: Images are automatically optimized for web

### Agricultural Content Focus
- Emphasize sustainability and innovation
- Highlight measurable results and achievements
- Use agricultural terminology appropriately
- Focus on benefits to farmers and agricultural businesses

## 🔧 Technical Features

### Responsive Design
- All content automatically adapts to different screen sizes
- Images are responsive and optimized for mobile

### SEO Optimization
- Meta descriptions and keywords are configurable
- Open Graph images for social media sharing
- Structured data for better search engine understanding

### Performance
- Images are lazy-loaded and optimized
- Content is cached for fast loading
- Minimal impact on site performance

## 🚨 Important Notes

### Content Publishing
1. **Editorial Workflow**: Changes go through review before publishing
2. **Preview**: Always preview changes before publishing
3. **Backup**: Content is automatically backed up in Git

### Image Management
1. **File Names**: Use descriptive, SEO-friendly file names
2. **Alt Text**: Always provide meaningful alt text for accessibility
3. **File Size**: Keep images under 2MB for optimal performance

### Best Practices
1. **Regular Updates**: Keep content fresh and current
2. **Consistency**: Maintain consistent tone and style
3. **Testing**: Test all links and functionality after updates
4. **Mobile**: Always check how content looks on mobile devices

## 🆘 Troubleshooting

### Common Issues
1. **Images Not Loading**: Check file path and ensure image is uploaded
2. **Content Not Updating**: Clear browser cache and refresh
3. **CMS Access Issues**: Verify Netlify account permissions

### Support
- Check the browser console for error messages
- Ensure all required fields are filled
- Verify image file formats (JPG, PNG, WebP supported)

## 🎯 Getting Started Checklist

1. ✅ Access the CMS at `/admin/`
2. ✅ Update Site Settings with your information
3. ✅ Upload your brand images
4. ✅ Customize the Home page content
5. ✅ Add your team members to About page
6. ✅ Configure contact information
7. ✅ Add your farm projects and services
8. ✅ Set up social media links
9. ✅ Review and publish changes

The CMS is now fully configured and ready for content management with proper agricultural theming, image handling, and user-friendly interfaces!
