<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Darvi Group CMS</title>
    <!-- Include the script that enables Netlify Identity -->
    <script src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap">
  </head>
  <body>
    <!-- Include the script that builds the page and powers Decap CMS (formerly Netlify CMS) -->
    <script src="https://unpkg.com/decap-cms@^3.0.0/dist/decap-cms.js"></script>
    <!-- Include custom configuration -->
    <script src="config.js"></script>
    <script>
      // Initialize the CMS
      if (window.netlifyIdentity) {
        window.netlifyIdentity.on("init", user => {
          if (!user) {
            window.netlifyIdentity.on("login", () => {
              document.location.href = "/admin/";
            });
          }
        });
      }

      // Debug information
      console.log("CMS initialized");

      // Add event listener for CMS ready
      if (window.CMS) {
        window.CMS.registerEventListener({
          name: 'preSave',
          handler: function(data) {
            console.log('Content about to be saved:', data);
          }
        });

        window.CMS.registerEventListener({
          name: 'postSave',
          handler: function(data) {
            console.log('Content saved:', data);
          }
        });
      }
    </script>
  </body>
</html>
