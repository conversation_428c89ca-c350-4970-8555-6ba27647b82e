import { useState, useEffect } from 'react'
import { motion, useInView } from 'framer-motion'
import { useRef } from 'react'
import styled from 'styled-components'

const CounterWrapper = styled(motion.div)`
  text-align: center;
`

const CounterNumber = styled(motion.div)`
  font-family: var(--font-heading);
  font-weight: 800;
  font-size: clamp(2rem, 4vw, 3rem);
  color: var(--primary-color);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`

const CounterLabel = styled.p`
  font-weight: 600;
  color: var(--text-dark);
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
`

const CounterIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
`

function AnimatedCounter({ 
  end, 
  label, 
  duration = 2000, 
  suffix = "", 
  prefix = "",
  icon = null,
  delay = 0 
}) {
  const [count, setCount] = useState(0)
  const [hasAnimated, setHasAnimated] = useState(false)
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, threshold: 0.3 })

  useEffect(() => {
    if (isInView && !hasAnimated) {
      setHasAnimated(true)
      
      const timer = setTimeout(() => {
        let startTime = null
        const startValue = 0
        const endValue = parseInt(end.toString().replace(/[^\d]/g, ''))

        const animate = (currentTime) => {
          if (startTime === null) startTime = currentTime
          const progress = Math.min((currentTime - startTime) / duration, 1)
          
          // Easing function for smooth animation
          const easeOutQuart = 1 - Math.pow(1 - progress, 4)
          const currentValue = Math.floor(startValue + (endValue - startValue) * easeOutQuart)
          
          setCount(currentValue)

          if (progress < 1) {
            requestAnimationFrame(animate)
          }
        }

        requestAnimationFrame(animate)
      }, delay)

      return () => clearTimeout(timer)
    }
  }, [isInView, hasAnimated, end, duration, delay])

  const formatNumber = (num) => {
    // Handle special cases like percentages
    if (end.toString().includes('%')) {
      return `${num}%`
    }
    if (end.toString().includes('+')) {
      return `${num.toLocaleString()}+`
    }
    if (end.toString().includes('K')) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return num.toLocaleString()
  }

  return (
    <CounterWrapper
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay: delay / 1000 }}
    >
      {icon && <CounterIcon>{icon}</CounterIcon>}
      <CounterNumber
        initial={{ scale: 0.5 }}
        animate={isInView ? { scale: 1 } : { scale: 0.5 }}
        transition={{ 
          duration: 0.5, 
          delay: delay / 1000,
          type: "spring",
          stiffness: 100
        }}
      >
        {prefix}{formatNumber(count)}{suffix}
      </CounterNumber>
      <CounterLabel>{label}</CounterLabel>
    </CounterWrapper>
  )
}

export default AnimatedCounter
