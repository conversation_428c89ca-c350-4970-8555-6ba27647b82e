import { useState } from 'react'
import { motion } from 'framer-motion'
import styled from 'styled-components'
import { FaUser, FaEnvelope, FaPhone, FaRupeeSign, FaCalendarAlt, FaMapMarkerAlt, FaComment, FaPaperPlane, FaCheckCircle } from 'react-icons/fa'
import SectionTitle from '../components/SectionTitle'
import CurvedDivider from '../components/CurvedDivider'

const EnquiryContainer = styled.div`
  padding-top: 100px;
  min-height: 100vh;
`

const HeroSection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(135deg, #f0f7eb 0%, #e8f5e3 100%);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%234a7c59" fill-opacity="0.1"/><circle cx="80" cy="80" r="2" fill="%234a7c59" fill-opacity="0.1"/></svg>');
    background-size: 100px 100px;
    pointer-events: none;
  }
`

const EnquirySection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: white;
`

const EnquiryGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  
  @media (max-width: 968px) {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
`

const EnquiryInfo = styled(motion.div)`
  padding: 2rem;
`

const InfoTitle = styled.h2`
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  color: var(--primary-dark);
  font-family: var(--font-heading);
  font-weight: 700;
  
  @media (max-width: 768px) {
    font-size: 2rem;
  }
`

const InfoDescription = styled.p`
  font-size: 1.2rem;
  color: var(--text-muted);
  line-height: 1.7;
  margin-bottom: 3rem;
`

const BenefitsList = styled.ul`
  list-style: none;
  padding: 0;
  margin-bottom: 3rem;
`

const BenefitItem = styled(motion.li)`
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(74, 124, 89, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(74, 124, 89, 0.1);
    transform: translateX(8px);
  }
  
  svg {
    color: var(--primary-color);
    margin-right: 1rem;
    font-size: 1.2rem;
  }
  
  span {
    color: var(--text-dark);
    font-weight: 500;
  }
`

const ContactQuickInfo = styled.div`
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  padding: 2rem;
  border-radius: 20px;
  text-align: center;
  
  h4 {
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 600;
  }
  
  p {
    margin-bottom: 0.5rem;
    opacity: 0.9;
  }
  
  .phone {
    font-size: 1.2rem;
    font-weight: 600;
    margin-top: 1rem;
  }
`

const EnquiryForm = styled(motion.form)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(74, 124, 89, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  
  @media (max-width: 768px) {
    padding: 2rem;
  }
`

const FormTitle = styled.h3`
  margin-bottom: 2rem;
  color: var(--primary-dark);
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1.8rem;
  text-align: center;
`

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
`

const FormGroup = styled.div`
  margin-bottom: 2rem;
  position: relative;
`

const FormLabel = styled.label`
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--primary-dark);
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  svg {
    color: var(--primary-color);
    font-size: 0.9rem;
  }
`

const FormInput = styled.input`
  width: 100%;
  padding: 1rem 1.5rem;
  border: 2px solid rgba(74, 124, 89, 0.2);
  border-radius: 12px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 4px rgba(74, 124, 89, 0.1);
  }
  
  &::placeholder {
    color: var(--text-muted);
  }
`

const FormSelect = styled.select`
  width: 100%;
  padding: 1rem 1.5rem;
  border: 2px solid rgba(74, 124, 89, 0.2);
  border-radius: 12px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 4px rgba(74, 124, 89, 0.1);
  }
`

const FormTextarea = styled.textarea`
  width: 100%;
  padding: 1rem 1.5rem;
  border: 2px solid rgba(74, 124, 89, 0.2);
  border-radius: 12px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  min-height: 120px;
  resize: vertical;
  font-family: inherit;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 4px rgba(74, 124, 89, 0.1);
  }
  
  &::placeholder {
    color: var(--text-muted);
  }
`

const SubmitButton = styled(motion.button)`
  width: 100%;
  padding: 1.25rem 2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(74, 124, 89, 0.3);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }
`

const benefits = [
  "Free consultation with agricultural experts",
  "Detailed site analysis and soil testing",
  "Customized investment plans",
  "Guaranteed 12% annual returns",
  "Regular progress updates and reports",
  "Professional farm management services"
]

const investmentTypes = [
  "Mango Orchard Development",
  "Organic Vegetable Farming",
  "Smart Greenhouse Technology",
  "Rice Cultivation",
  "Medicinal Plants",
  "Integrated Dairy & Fodder",
  "Custom Investment Plan"
]

const budgetRanges = [
  "₹1-5 Lakhs",
  "₹5-10 Lakhs",
  "₹10-25 Lakhs",
  "₹25-50 Lakhs",
  "₹50 Lakhs - 1 Crore",
  "Above ₹1 Crore"
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: "easeOut"
    }
  }
}

function Enquiry() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    investmentType: '',
    budget: '',
    location: '',
    timeline: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    alert('Thank you for your enquiry! Our team will contact you within 24 hours.')
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      investmentType: '',
      budget: '',
      location: '',
      timeline: '',
      message: ''
    })
    setIsSubmitting(false)
  }

  return (
    <EnquiryContainer>
      <HeroSection
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className="container">
          <SectionTitle
            subtitle="Investment Enquiry"
            title="Start Your Farmland Investment Journey"
            description="Ready to invest in sustainable agriculture? Fill out our enquiry form and our experts will guide you through the best investment opportunities tailored to your goals."
          />
        </div>
      </HeroSection>
      
      <CurvedDivider type="wave1" fillColor="white" opacity={1} />
      
      <EnquirySection
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="container">
          <EnquiryGrid>
            <EnquiryInfo
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              <InfoTitle>Why Choose Darvi Farmlands?</InfoTitle>
              <InfoDescription>
                Join hundreds of satisfied investors who have already benefited from our professional 
                farmland management services. Get personalized consultation and start your investment 
                journey with confidence.
              </InfoDescription>
              
              <BenefitsList>
                {benefits.map((benefit, index) => (
                  <BenefitItem
                    key={index}
                    variants={itemVariants}
                    whileHover={{ scale: 1.02 }}
                  >
                    <FaCheckCircle />
                    <span>{benefit}</span>
                  </BenefitItem>
                ))}
              </BenefitsList>
              
              <ContactQuickInfo>
                <h4>Need Immediate Assistance?</h4>
                <p>Speak directly with our investment experts</p>
                <div className="phone">+91 99868 90777</div>
                <p>Mon-Sat 9:00 AM - 6:00 PM</p>
              </ContactQuickInfo>
            </EnquiryInfo>
            
            <EnquiryForm
              onSubmit={handleSubmit}
              variants={itemVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              <FormTitle>Investment Enquiry Form</FormTitle>
              
              <FormRow>
                <FormGroup>
                  <FormLabel htmlFor="firstName">
                    <FaUser />
                    First Name
                  </FormLabel>
                  <FormInput
                    type="text"
                    id="firstName"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    placeholder="Enter your first name"
                    required
                  />
                </FormGroup>
                
                <FormGroup>
                  <FormLabel htmlFor="lastName">
                    <FaUser />
                    Last Name
                  </FormLabel>
                  <FormInput
                    type="text"
                    id="lastName"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    placeholder="Enter your last name"
                    required
                  />
                </FormGroup>
              </FormRow>
              
              <FormRow>
                <FormGroup>
                  <FormLabel htmlFor="email">
                    <FaEnvelope />
                    Email Address
                  </FormLabel>
                  <FormInput
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter your email"
                    required
                  />
                </FormGroup>
                
                <FormGroup>
                  <FormLabel htmlFor="phone">
                    <FaPhone />
                    Phone Number
                  </FormLabel>
                  <FormInput
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="Enter your phone number"
                    required
                  />
                </FormGroup>
              </FormRow>
              
              <FormRow>
                <FormGroup>
                  <FormLabel htmlFor="investmentType">
                    <FaComment />
                    Investment Type
                  </FormLabel>
                  <FormSelect
                    id="investmentType"
                    name="investmentType"
                    value={formData.investmentType}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select investment type</option>
                    {investmentTypes.map((type, index) => (
                      <option key={index} value={type}>{type}</option>
                    ))}
                  </FormSelect>
                </FormGroup>
                
                <FormGroup>
                  <FormLabel htmlFor="budget">
                    <FaRupeeSign />
                    Investment Budget
                  </FormLabel>
                  <FormSelect
                    id="budget"
                    name="budget"
                    value={formData.budget}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select budget range</option>
                    {budgetRanges.map((range, index) => (
                      <option key={index} value={range}>{range}</option>
                    ))}
                  </FormSelect>
                </FormGroup>
              </FormRow>
              
              <FormRow>
                <FormGroup>
                  <FormLabel htmlFor="location">
                    <FaMapMarkerAlt />
                    Preferred Location
                  </FormLabel>
                  <FormInput
                    type="text"
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    placeholder="e.g., Raichur, Belgaum, Hubli"
                  />
                </FormGroup>
                
                <FormGroup>
                  <FormLabel htmlFor="timeline">
                    <FaCalendarAlt />
                    Investment Timeline
                  </FormLabel>
                  <FormSelect
                    id="timeline"
                    name="timeline"
                    value={formData.timeline}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select timeline</option>
                    <option value="immediate">Immediate (Within 1 month)</option>
                    <option value="3months">Within 3 months</option>
                    <option value="6months">Within 6 months</option>
                    <option value="1year">Within 1 year</option>
                    <option value="exploring">Just exploring options</option>
                  </FormSelect>
                </FormGroup>
              </FormRow>
              
              <FormGroup>
                <FormLabel htmlFor="message">
                  <FaComment />
                  Additional Requirements
                </FormLabel>
                <FormTextarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  placeholder="Tell us about your specific requirements, questions, or any additional information that would help us serve you better..."
                />
              </FormGroup>
              
              <SubmitButton
                type="submit"
                disabled={isSubmitting}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {isSubmitting ? 'Submitting...' : 'Submit Enquiry'}
                <FaPaperPlane />
              </SubmitButton>
            </EnquiryForm>
          </EnquiryGrid>
        </div>
      </EnquirySection>
      
      <CurvedDivider type="wave2" fillColor="var(--primary-color)" opacity={0.1} />
    </EnquiryContainer>
  )
}

export default Enquiry
    