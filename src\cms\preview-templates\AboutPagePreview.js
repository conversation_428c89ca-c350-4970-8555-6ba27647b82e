import React from 'react';
import { StyleSheetInjector } from '../cms.jsx';
import styled from 'styled-components';

const PreviewContainer = styled.div`
  font-family: 'Montserrat', sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
`;

const HeroPreview = styled.div`
  background-color: #f5f5f5;
  background-image: ${props => props.backgroundImage ? `url(${props.backgroundImage})` : 'none'};
  background-size: cover;
  background-position: center;
  padding: 100px 20px;
  text-align: center;
  color: white;
  border-radius: 8px;
  margin-bottom: 30px;
`;

const HeroTitle = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 1rem;
`;

const HeroSubtitle = styled.p`
  font-size: 1.2rem;
  margin-bottom: 2rem;
`;

const SectionTitle = styled.div`
  text-align: center;
  margin-bottom: 30px;
`;

const SectionSubtitle = styled.p`
  color: #4caf50;
  text-transform: uppercase;
  font-weight: 600;
  margin-bottom: 0.5rem;
`;

const SectionHeading = styled.h2`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const SectionDescription = styled.p`
  max-width: 700px;
  margin: 0 auto;
  opacity: 0.8;
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 40px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const ContentImage = styled.div`
  height: 300px;
  background-image: ${props => props.image ? `url(${props.image})` : 'none'};
  background-size: cover;
  background-position: center;
  border-radius: 8px;
`;

const ContentText = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

const Paragraph = styled.p`
  margin-bottom: 15px;
  line-height: 1.6;
`;

const TeamGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
`;

const TeamMember = styled.div`
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const MemberImage = styled.div`
  height: 200px;
  background-image: ${props => props.image ? `url(${props.image})` : 'none'};
  background-size: cover;
  background-position: center;
`;

const MemberInfo = styled.div`
  padding: 20px;
`;

const MemberName = styled.h3`
  margin-bottom: 5px;
`;

const MemberRole = styled.p`
  color: #4caf50;
  font-weight: 600;
  margin-bottom: 10px;
  font-size: 0.9rem;
`;

const MemberBio = styled.p`
  font-size: 0.9rem;
  opacity: 0.8;
`;

const ValuesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
`;

const ValueCard = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const ValueTitle = styled.h3`
  margin-bottom: 10px;
  color: #4caf50;
`;

const ValueDescription = styled.p`
  font-size: 0.9rem;
  opacity: 0.8;
`;

const AboutPagePreview = ({ entry }) => {
  const data = entry.getIn(['data']).toJS();

  return (
    <StyleSheetInjector>
      <PreviewContainer>
        <h2>About Page Preview</h2>

        <HeroPreview backgroundImage={data.heroBackgroundImage}>
          <HeroTitle>{data.heroTitle || 'About Darvi Group'}</HeroTitle>
          <HeroSubtitle>{data.heroSubtitle || 'Your trusted partner in premium nursery plants and agricultural solutions.'}</HeroSubtitle>
          <div>
            <button>{data.primaryBtnText || 'Primary Button'}</button>
          </div>
        </HeroPreview>

        {data.storySection && (
          <div>
            <SectionTitle>
              <SectionSubtitle>{data.storySection.subtitle || 'Our Story'}</SectionSubtitle>
              <SectionHeading>{data.storySection.title || 'Company History and Mission'}</SectionHeading>
              <SectionDescription>{data.storySection.description || 'Description'}</SectionDescription>
            </SectionTitle>

            {(data.contentBlocks || []).map((block, index) => (
              <ContentGrid key={index}>
                {index % 2 === 0 ? (
                  <>
                    <ContentImage image={block.image} />
                    <ContentText>
                      <Paragraph>{block.content || 'Content block'}</Paragraph>
                    </ContentText>
                  </>
                ) : (
                  <>
                    <ContentText>
                      <Paragraph>{block.content || 'Content block'}</Paragraph>
                    </ContentText>
                    <ContentImage image={block.image} />
                  </>
                )}
              </ContentGrid>
            ))}
          </div>
        )}

        {data.teamSection && (
          <div>
            <SectionTitle>
              <SectionSubtitle>{data.teamSection.subtitle || 'Our Team'}</SectionSubtitle>
              <SectionHeading>{data.teamSection.title || 'Meet the Experts'}</SectionHeading>
              <SectionDescription>{data.teamSection.description || 'Description'}</SectionDescription>
            </SectionTitle>

            <TeamGrid>
              {(data.teamMembers || []).map((member, index) => (
                <TeamMember key={index}>
                  <MemberImage image={member.image} />
                  <MemberInfo>
                    <MemberName>{member.name || `Team Member ${index + 1}`}</MemberName>
                    <MemberRole>{member.role || 'Role'}</MemberRole>
                    <MemberBio>{member.bio || 'Bio'}</MemberBio>
                  </MemberInfo>
                </TeamMember>
              ))}
            </TeamGrid>
          </div>
        )}

        {data.valuesSection && (
          <div>
            <SectionTitle>
              <SectionSubtitle>{data.valuesSection.subtitle || 'Our Values'}</SectionSubtitle>
              <SectionHeading>{data.valuesSection.title || 'What Drives Us'}</SectionHeading>
              <SectionDescription>{data.valuesSection.description || 'Description'}</SectionDescription>
            </SectionTitle>

            <ValuesGrid>
              {(data.values || []).map((value, index) => (
                <ValueCard key={index}>
                  <ValueTitle>{value.title || `Value ${index + 1}`}</ValueTitle>
                  <ValueDescription>{value.description || 'Description'}</ValueDescription>
                </ValueCard>
              ))}
            </ValuesGrid>
          </div>
        )}
      </PreviewContainer>
    </StyleSheetInjector>
  );
};

export default AboutPagePreview;
