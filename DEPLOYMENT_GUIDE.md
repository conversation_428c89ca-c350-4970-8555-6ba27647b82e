# Darvi Group Website Deployment Guide

This guide provides instructions for deploying the Darvi Group website with Decap CMS (formerly Netlify CMS) to Netlify.

## Prerequisites

- A GitHub account
- A Netlify account
- Node.js and npm installed on your local machine

## Local Development

1. Clone the repository:
   ```
   git clone https://github.com/your-username/darvi-farm-website.git
   cd darvi-farm-website
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Run the setup script to ensure all necessary directories exist:
   ```
   npm run setup-cms
   ```

4. Start the development server:
   ```
   npm run dev
   ```

5. Open your browser and navigate to `http://localhost:5173` to view the website.

## Deploying to Netlify

### Option 1: Deploy via Netlify UI

1. Log in to your Netlify account.
2. Click "New site from Git".
3. Choose G<PERSON>ub as your Git provider and authorize Netlify.
4. Select your repository.
5. Configure the build settings:
   - Build command: `npm run build`
   - Publish directory: `dist`
6. Click "Deploy site".

### Option 2: Deploy via Netlify CLI

1. Install the Netlify CLI:
   ```
   npm install -g netlify-cli
   ```

2. Log in to Netlify:
   ```
   netlify login
   ```

3. Initialize your site:
   ```
   netlify init
   ```

4. Follow the prompts to configure your site.

5. Deploy your site:
   ```
   netlify deploy --prod
   ```

## Setting Up Decap CMS

After deploying your site to Netlify, you need to configure Netlify Identity and Git Gateway to enable the CMS:

1. Go to your site's Netlify dashboard.
2. Navigate to "Site settings" > "Identity".
3. Click "Enable Identity".
4. Under "Registration preferences", select "Invite only".
5. Under "External providers", you can enable login with GitHub, GitLab, etc. (optional).
6. Navigate to "Services" > "Git Gateway" and enable it.

## Inviting CMS Users

1. Go to your site's Netlify dashboard.
2. Navigate to "Identity" > "Invite users".
3. Enter the email addresses of the users you want to invite.
4. Click "Send".

The invited users will receive an email with a link to accept the invitation and set up their account.

## Accessing the CMS

Once your site is deployed and Netlify Identity is set up:

1. Navigate to `https://your-site-name.netlify.app/admin/`.
2. Log in with your Netlify Identity credentials.
3. You can now manage your site's content through the CMS.

## Troubleshooting

### CMS Not Loading

If the CMS doesn't load properly:

1. Check the browser console for errors.
2. Ensure Netlify Identity and Git Gateway are enabled.
3. Verify that the `admin/index.html` and `admin/config.yml` files are correctly deployed.

### Authentication Issues

If users can't log in:

1. Check if they've accepted their invitation.
2. Ensure they're using the correct email address.
3. Try resetting their password through the Netlify Identity widget.

### Content Not Saving

If changes made in the CMS aren't being saved:

1. Check if Git Gateway is properly configured.
2. Verify that the user has the necessary permissions.
3. Check the Netlify deploy logs for any errors.

## Additional Resources

- [Decap CMS Documentation](https://decapcms.org/docs/intro/)
- [Netlify Identity Documentation](https://docs.netlify.com/visitor-access/identity/)
- [Git Gateway Documentation](https://docs.netlify.com/visitor-access/git-gateway/)

For more detailed instructions on using the CMS, refer to the `CMS_INSTRUCTIONS.md` file.
