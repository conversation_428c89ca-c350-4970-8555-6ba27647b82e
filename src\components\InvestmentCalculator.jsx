import { useState } from 'react'
import { motion } from 'framer-motion'
import styled from 'styled-components'
import { FaCalculator, FaChartLine, FaRupeeSign } from 'react-icons/fa'

const CalculatorSection = styled(motion.section)`
  background: linear-gradient(135deg, #f0f7eb 0%, #e8f5e3 100%);
  padding: var(--spacing-xxl) 0;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60"><circle cx="30" cy="30" r="2" fill="%234a7c59" fill-opacity="0.1"/></svg>');
    background-size: 60px 60px;
    pointer-events: none;
  }
`

const CalculatorContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  
  @media (max-width: 968px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
`

const CalculatorCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(74, 124, 89, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  }
  
  @media (max-width: 768px) {
    padding: 2rem;
  }
`

const FormGroup = styled.div`
  margin-bottom: 2rem;
`

const Label = styled.label`
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--primary-dark);
  font-size: 1.1rem;
`

const InputWrapper = styled.div`
  position: relative;
`

const Input = styled.input`
  width: 100%;
  padding: 1rem 1.5rem;
  border: 2px solid rgba(74, 124, 89, 0.2);
  border-radius: 12px;
  font-size: 1.1rem;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 4px rgba(74, 124, 89, 0.1);
  }
  
  &[type="range"] {
    height: 8px;
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
    border-radius: 4px;
    appearance: none;
    
    &::-webkit-slider-thumb {
      appearance: none;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: var(--primary-color);
      cursor: pointer;
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    &::-moz-range-thumb {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: var(--primary-color);
      cursor: pointer;
      border: none;
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
  }
`

const Select = styled.select`
  width: 100%;
  padding: 1rem 1.5rem;
  border: 2px solid rgba(74, 124, 89, 0.2);
  border-radius: 12px;
  font-size: 1.1rem;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 4px rgba(74, 124, 89, 0.1);
  }
`

const RangeValue = styled.div`
  text-align: center;
  margin-top: 0.5rem;
  font-weight: 600;
  color: var(--primary-color);
  font-size: 1.2rem;
`

const ResultsCard = styled(motion.div)`
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  border-radius: 20px;
  padding: 2.5rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
  }
  
  @keyframes shimmer {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(180deg); }
  }
`

const ResultTitle = styled.h3`
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  color: white;
`

const ResultValue = styled.div`
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
  font-family: var(--font-heading);
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
`

const ResultSubtext = styled.p`
  opacity: 0.9;
  font-size: 1.1rem;
  margin-bottom: 0;
`

const CalculateButton = styled(motion.button)`
  width: 100%;
  padding: 1.25rem 2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(74, 124, 89, 0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
`

const InfoSection = styled(motion.div)`
  text-align: center;
  max-width: 600px;
`

const InfoTitle = styled.h2`
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  color: var(--primary-dark);
  font-family: var(--font-heading);
`

const InfoText = styled.p`
  font-size: 1.2rem;
  color: var(--text-muted);
  line-height: 1.7;
  margin-bottom: 2rem;
`

const FeatureList = styled.ul`
  list-style: none;
  padding: 0;
  text-align: left;
  
  li {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: var(--text-dark);
    
    &::before {
      content: '✓';
      color: var(--primary-color);
      font-weight: bold;
      margin-right: 1rem;
      font-size: 1.2rem;
    }
  }
`

function InvestmentCalculator() {
  const [amount, setAmount] = useState(500000)
  const [duration, setDuration] = useState(3)
  const [results, setResults] = useState(null)

  const calculateReturns = () => {
    // Sample calculation - 12% annual return
    const annualReturn = 0.12
    const totalReturn = amount * Math.pow(1 + annualReturn, duration)
    const profit = totalReturn - amount
    
    setResults({
      totalReturn: Math.round(totalReturn),
      profit: Math.round(profit),
      monthlyReturn: Math.round(profit / (duration * 12))
    })
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(value)
  }

  return (
    <CalculatorSection
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
    >
      <div className="container">
        <CalculatorContainer>
          <InfoSection
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <InfoTitle>Smart Investment Calculator</InfoTitle>
            <InfoText>
              Calculate your potential returns from farmland investments. Our managed farmlands offer consistent returns with professional agricultural management.
            </InfoText>
            <FeatureList>
              <li>Guaranteed 12% annual returns</li>
              <li>Professional farm management</li>
              <li>Transparent investment process</li>
              <li>Regular progress updates</li>
              <li>Sustainable farming practices</li>
            </FeatureList>
          </InfoSection>

          <CalculatorCard
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <FormGroup>
              <Label>Investment Amount</Label>
              <InputWrapper>
                <Input
                  type="range"
                  min="100000"
                  max="5000000"
                  step="50000"
                  value={amount}
                  onChange={(e) => setAmount(parseInt(e.target.value))}
                />
                <RangeValue>{formatCurrency(amount)}</RangeValue>
              </InputWrapper>
            </FormGroup>

            <FormGroup>
              <Label>Investment Duration</Label>
              <Select
                value={duration}
                onChange={(e) => setDuration(parseInt(e.target.value))}
              >
                <option value={1}>1 Year</option>
                <option value={2}>2 Years</option>
                <option value={3}>3 Years</option>
                <option value={4}>4 Years</option>
                <option value={5}>5 Years</option>
              </Select>
            </FormGroup>

            <CalculateButton
              onClick={calculateReturns}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <FaCalculator />
              Calculate Returns
            </CalculateButton>

            {results && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                style={{ marginTop: '2rem' }}
              >
                <ResultsCard>
                  <ResultTitle>Your Investment Returns</ResultTitle>
                  <ResultValue>{formatCurrency(results.totalReturn)}</ResultValue>
                  <ResultSubtext>
                    Total Value after {duration} year{duration > 1 ? 's' : ''}
                  </ResultSubtext>
                  <div style={{ marginTop: '1.5rem', paddingTop: '1.5rem', borderTop: '1px solid rgba(255,255,255,0.2)' }}>
                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem', textAlign: 'center' }}>
                      <div>
                        <div style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>{formatCurrency(results.profit)}</div>
                        <div style={{ opacity: 0.8 }}>Total Profit</div>
                      </div>
                      <div>
                        <div style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>{formatCurrency(results.monthlyReturn)}</div>
                        <div style={{ opacity: 0.8 }}>Monthly Returns</div>
                      </div>
                    </div>
                  </div>
                </ResultsCard>
              </motion.div>
            )}
          </CalculatorCard>
        </CalculatorContainer>
      </div>
    </CalculatorSection>
  )
}

export default InvestmentCalculator