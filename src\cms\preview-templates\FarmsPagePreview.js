import React from 'react';
import { StyleSheetInjector } from '../cms.jsx';
import styled from 'styled-components';

const PreviewContainer = styled.div`
  font-family: 'Montserrat', sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
`;

const FarmsPagePreview = ({ entry }) => {
  const data = entry.getIn(['data']).toJS();

  return (
    <StyleSheetInjector>
      <PreviewContainer>
        <h2>Farms Page Preview</h2>
        <p>This is a placeholder for the Farms page preview.</p>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </PreviewContainer>
    </StyleSheetInjector>
  );
};

export default FarmsPagePreview;
