import { motion } from 'framer-motion'
import styled from 'styled-components'
import { <PERSON>aA<PERSON>, FaCert<PERSON>ate, FaTrophy, FaStar } from 'react-icons/fa'
import SectionTitle from './SectionTitle'

const AwardsSection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="120" height="120" viewBox="0 0 120 120"><circle cx="60" cy="60" r="3" fill="%23f4a261" fill-opacity="0.1"/><circle cx="20" cy="20" r="2" fill="%234a7c59" fill-opacity="0.08"/><circle cx="100" cy="100" r="2" fill="%234a7c59" fill-opacity="0.08"/></svg>');
    background-size: 120px 120px;
    pointer-events: none;
  }
`

const AwardsContainer = styled.div`
  position: relative;
  z-index: 1;
`

const AwardsGrid = styled(motion.div)`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  margin-top: 3rem;
  
  @media (max-width: 968px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
`

const AwardsImage = styled(motion.div)`
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  height: 400px;
  background-size: cover;
  background-position: center;
  box-shadow: 0 20px 40px rgba(74, 124, 89, 0.15);
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(74, 124, 89, 0.1), rgba(244, 162, 97, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover::before {
    opacity: 1;
  }
  
  @media (max-width: 768px) {
    height: 300px;
  }
`

const AwardsContent = styled(motion.div)`
  padding: 2rem;
  
  @media (max-width: 768px) {
    padding: 1rem;
  }
`

const AwardsTitle = styled.h2`
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  color: var(--primary-dark);
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.2;
  
  @media (max-width: 768px) {
    font-size: 2rem;
  }
`

const AwardsDescription = styled.p`
  font-size: 1.2rem;
  color: var(--text-muted);
  line-height: 1.7;
  margin-bottom: 2.5rem;
  
  @media (max-width: 768px) {
    font-size: 1.1rem;
  }
`

const AwardsList = styled.div`
  display: grid;
  gap: 1.5rem;
`

const AwardItem = styled(motion.div)`
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(74, 124, 89, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateX(8px);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 8px 25px rgba(74, 124, 89, 0.12);
  }
  
  @media (max-width: 768px) {
    padding: 1.25rem;
  }
`

const AwardIcon = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.5rem;
  flex-shrink: 0;
  
  svg {
    font-size: 1.5rem;
    color: white;
  }
  
  @media (max-width: 768px) {
    width: 50px;
    height: 50px;
    margin-right: 1rem;
    
    svg {
      font-size: 1.25rem;
    }
  }
`

const AwardContent = styled.div`
  flex: 1;
`

const AwardTitle = styled.h4`
  margin-bottom: 0.5rem;
  color: var(--primary-dark);
  font-weight: 600;
  font-size: 1.2rem;
  
  @media (max-width: 768px) {
    font-size: 1.1rem;
  }
`

const AwardSubtitle = styled.p`
  color: var(--text-muted);
  font-size: 0.95rem;
  margin: 0;
  line-height: 1.4;
`

const RecognitionBadges = styled(motion.div)`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
`

const Badge = styled(motion.div)`
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(74, 124, 89, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(74, 124, 89, 0.15);
    background: rgba(255, 255, 255, 1);
  }
  
  @media (max-width: 768px) {
    padding: 1.5rem;
  }
`

const BadgeIcon = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  
  svg {
    font-size: 2rem;
    color: white;
  }
  
  @media (max-width: 768px) {
    width: 60px;
    height: 60px;
    
    svg {
      font-size: 1.5rem;
    }
  }
`

const BadgeTitle = styled.h5`
  margin-bottom: 0.5rem;
  color: var(--primary-dark);
  font-weight: 600;
  font-size: 1.1rem;
`

const BadgeText = styled.p`
  color: var(--text-muted);
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
`

const awards = [
  {
    icon: <FaAward />,
    title: "Best Agricultural Innovation Award 2023",
    subtitle: "Recognized for implementing sustainable farming practices and technology integration"
  },
  {
    icon: <FaCertificate />,
    title: "Certified Organic Farming Excellence",
    subtitle: "Certified by National Organic Standards for maintaining highest quality organic practices"
  },
  {
    icon: <FaTrophy />,
    title: "Top Investment Returns Provider",
    subtitle: "Awarded for consistently delivering promised returns to investors over 5 years"
  },
  {
    icon: <FaStar />,
    title: "Customer Satisfaction Excellence",
    subtitle: "Recognized for outstanding customer service and investor satisfaction ratings"
  }
]

const recognitionBadges = [
  {
    icon: <FaCertificate />,
    title: "ISO Certified",
    text: "Quality Management"
  },
  {
    icon: <FaAward />,
    title: "5-Star Rated",
    text: "Customer Reviews"
  },
  {
    icon: <FaTrophy />,
    title: "Industry Leader",
    text: "Farmland Investment"
  },
  {
    icon: <FaStar />,
    title: "Trusted Partner",
    text: "500+ Investors"
  }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
}

const badgeVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: "easeOut"
    }
  }
}

function Awards() {
  return (
    <AwardsSection
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
    >
      <div className="container">
        <SectionTitle
          subtitle="Recognition"
          title="Awards & Recognition"
          description="Our commitment to excellence in farmland management and investor satisfaction has earned us industry recognition and awards."
        />
        
        <AwardsContainer>
          <AwardsGrid
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
          >
            <AwardsContent variants={itemVariants}>
              <AwardsTitle>Recognized Excellence in Agricultural Investment</AwardsTitle>
              <AwardsDescription>
                Our dedication to sustainable farming practices, innovative agricultural solutions, and consistent investor returns has earned us multiple industry awards and certifications.
              </AwardsDescription>
              
              <AwardsList>
                {awards.map((award, index) => (
                  <AwardItem
                    key={index}
                    variants={itemVariants}
                    whileHover={{ scale: 1.02 }}
                  >
                    <AwardIcon>
                      {award.icon}
                    </AwardIcon>
                    <AwardContent>
                      <AwardTitle>{award.title}</AwardTitle>
                      <AwardSubtitle>{award.subtitle}</AwardSubtitle>
                    </AwardContent>
                  </AwardItem>
                ))}
              </AwardsList>
            </AwardsContent>
            
            <AwardsImage
              variants={itemVariants}
              style={{ 
                backgroundImage: `url(https://images.unsplash.com/photo-1677640724372-adb865d29aa8?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwzfHxhd2FyZCUyMGNlcnRpZmljYXRlJTIwYnVzaW5lc3MlMjByZWNvZ25pdGlvbnxlbnwwfDB8fHwxNzU0NDE2NzE3fDA&ixlib=rb-4.1.0&q=85)` 
              }}
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.3 }}
            />
          </AwardsGrid>
          
          <RecognitionBadges
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-50px" }}
          >
            {recognitionBadges.map((badge, index) => (
              <Badge
                key={index}
                variants={badgeVariants}
                whileHover={{ scale: 1.05 }}
              >
                <BadgeIcon>
                  {badge.icon}
                </BadgeIcon>
                <BadgeTitle>{badge.title}</BadgeTitle>
                <BadgeText>{badge.text}</BadgeText>
              </Badge>
            ))}
          </RecognitionBadges>
        </AwardsContainer>
      </div>
    </AwardsSection>
  )
}

export default Awards