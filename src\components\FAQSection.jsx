import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import styled from 'styled-components'
import { FaPlus, FaMinus } from 'react-icons/fa'
import SectionTitle from './SectionTitle'

const FAQSection = styled(motion.section)`
  padding: var(--spacing-xxl) 0;
  background: linear-gradient(to bottom, #f8f9fa, #ffffff);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60"><circle cx="30" cy="30" r="1.5" fill="%234a7c59" fill-opacity="0.06"/></svg>');
    background-size: 60px 60px;
    pointer-events: none;
  }
`

const FAQContainer = styled.div`
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
`

const FAQList = styled(motion.div)`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`

const FAQItem = styled(motion.div)`
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(74, 124, 89, 0.1);
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(74, 124, 89, 0.08);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 12px 35px rgba(74, 124, 89, 0.12);
    transform: translateY(-2px);
  }
`

const FAQQuestion = styled(motion.button)`
  width: 100%;
  padding: 1.5rem 2rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-dark);
  transition: all 0.3s ease;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
  }
  
  &:hover {
    color: var(--primary-color);
    
    &::before {
      transform: scaleX(1);
    }
  }
  
  @media (max-width: 768px) {
    padding: 1.25rem 1.5rem;
    font-size: 1rem;
  }
`

const QuestionText = styled.span`
  flex: 1;
  margin-right: 1rem;
  line-height: 1.4;
`

const IconWrapper = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.3s ease;
  
  svg {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
  }
  
  ${FAQQuestion}:hover & {
    transform: scale(1.1);
    background: linear-gradient(135deg, var(--primary-light), var(--accent-color));
  }
  
  @media (max-width: 768px) {
    width: 28px;
    height: 28px;
    
    svg {
      font-size: 0.8rem;
    }
  }
`

const FAQAnswer = styled(motion.div)`
  padding: 0 2rem 2rem;
  color: var(--text-muted);
  line-height: 1.7;
  font-size: 1rem;
  
  p {
    margin-bottom: 1rem;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
    
    li {
      margin-bottom: 0.5rem;
      position: relative;
      
      &::marker {
        color: var(--primary-color);
      }
    }
  }
  
  @media (max-width: 768px) {
    padding: 0 1.5rem 1.5rem;
    font-size: 0.95rem;
  }
`

const faqData = [
  {
    question: "What is the minimum investment amount for farmland?",
    answer: "The minimum investment amount is ₹1 Lakh. This allows you to start your farmland investment journey with a manageable amount while still benefiting from our professional management services and guaranteed returns."
  },
  {
    question: "How are the 12% annual returns guaranteed?",
    answer: "Our guaranteed 12% annual returns are backed by our proven agricultural management expertise, diversified crop portfolio, and strategic partnerships with agricultural markets. We have a track record of consistent performance and maintain insurance coverage to protect your investment."
  },
  {
    question: "What happens if the crops fail or there are natural disasters?",
    answer: "We maintain comprehensive crop insurance and disaster protection policies for all our managed farmlands. In case of crop failure or natural disasters, your investment is protected through our insurance coverage, ensuring you still receive your guaranteed returns."
  },
  {
    question: "Can I visit my farmland investment?",
    answer: "Absolutely! We encourage investors to visit their farmland. We organize regular site visits and provide detailed progress reports. You can schedule visits at any time to see your investment firsthand and meet with our farm management team."
  },
  {
    question: "How often will I receive updates on my investment?",
    answer: "You'll receive monthly progress reports with detailed updates on crop growth, weather conditions, farming activities, and financial performance. Additionally, you'll have access to our investor portal for real-time updates and can contact our team anytime for specific queries."
  },
  {
    question: "What types of crops are grown on the farmland?",
    answer: "We grow a diversified mix of crops including premium mangoes, organic vegetables, cash crops, and seasonal produce. Our agricultural experts select crops based on soil conditions, climate, market demand, and profitability to ensure optimal returns for investors."
  },
  {
    question: "Is there a lock-in period for investments?",
    answer: "Yes, there is a minimum lock-in period of 1 year to allow for proper crop cycles and farm development. However, you can choose investment terms ranging from 1 to 5 years based on your financial goals and preferences."
  },
  {
    question: "How do I track the performance of my investment?",
    answer: "We provide a comprehensive investor dashboard where you can track your investment performance, view progress photos, access financial reports, and monitor crop growth in real-time. You'll also receive regular email updates and can schedule calls with our team."
  }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: "easeOut"
    }
  }
}

const answerVariants = {
  hidden: {
    opacity: 0,
    height: 0,
    transition: {
      duration: 0.3,
      ease: "easeInOut"
    }
  },
  visible: {
    opacity: 1,
    height: "auto",
    transition: {
      duration: 0.3,
      ease: "easeInOut"
    }
  }
}

function FAQ() {
  const [openItems, setOpenItems] = useState(new Set([0])) // First item open by default

  const toggleItem = (index) => {
    const newOpenItems = new Set(openItems)
    if (newOpenItems.has(index)) {
      newOpenItems.delete(index)
    } else {
      newOpenItems.add(index)
    }
    setOpenItems(newOpenItems)
  }

  return (
    <FAQSection
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
    >
      <div className="container">
        <SectionTitle
          subtitle="FAQ"
          title="Frequently Asked Questions"
          description="Get answers to common questions about farmland investment, returns, and our management services."
        />
        
        <FAQContainer>
          <FAQList
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-50px" }}
          >
            {faqData.map((faq, index) => (
              <FAQItem
                key={index}
                variants={itemVariants}
                layout
              >
                <FAQQuestion
                  onClick={() => toggleItem(index)}
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                >
                  <QuestionText>{faq.question}</QuestionText>
                  <IconWrapper>
                    <motion.div
                      animate={{ rotate: openItems.has(index) ? 180 : 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      {openItems.has(index) ? <FaMinus /> : <FaPlus />}
                    </motion.div>
                  </IconWrapper>
                </FAQQuestion>
                
                <AnimatePresence>
                  {openItems.has(index) && (
                    <FAQAnswer
                      variants={answerVariants}
                      initial="hidden"
                      animate="visible"
                      exit="hidden"
                    >
                      <p>{faq.answer}</p>
                    </FAQAnswer>
                  )}
                </AnimatePresence>
              </FAQItem>
            ))}
          </FAQList>
        </FAQContainer>
      </div>
    </FAQSection>
  )
}

export default FAQ