import { motion } from 'framer-motion'
import styled from 'styled-components'

const Card = styled(motion.div)`
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--box-shadow-lg);
  border: 1px solid rgba(74, 124, 89, 0.1);
  transition: all var(--transition-speed) var(--transition-smooth);
  height: 100%;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow: var(--box-shadow-xl);
  }

  @media (max-width: 768px) {
    padding: var(--spacing-lg);

    &:hover {
      transform: translateY(-4px);
    }
  }

  @media (max-width: 480px) {
    padding: var(--spacing-md);
  }
`

const IconWrapper = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  transition: all var(--transition-speed);

  svg {
    font-size: 2rem;
    color: var(--text-light);
  }

  ${Card}:hover & {
    transform: scale(1.1) rotate(5deg);
  }

  @media (max-width: 768px) {
    width: 60px;
    height: 60px;
    margin-bottom: var(--spacing-md);

    svg {
      font-size: 1.5rem;
    }

    ${Card}:hover & {
      transform: scale(1.05) rotate(3deg);
    }
  }

  @media (max-width: 480px) {
    width: 50px;
    height: 50px;

    svg {
      font-size: 1.25rem;
    }
  }
`

const Title = styled.h3`
  margin-bottom: var(--spacing-md);
  color: var(--primary-dark);
  font-family: var(--font-heading);
  font-weight: 600;
`

const Description = styled.p`
  color: var(--text-muted);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
`

const List = styled.ul`
  list-style: none;
  padding: 0;
  margin-top: var(--spacing-md);

  li {
    color: var(--text-dark);
    margin-bottom: 0.75rem;
    position: relative;
    padding-left: 2rem;
    font-size: 0.9rem;
    line-height: 1.5;

    &:before {
      content: '🌱';
      position: absolute;
      left: 0;
      top: 0;
      font-size: 1rem;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
`

function ServiceCard({ icon, title, description, items }) {
  return (
    <Card
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
    >
      <IconWrapper>
        {icon}
      </IconWrapper>
      <Title>{title}</Title>
      <Description>{description}</Description>
      {items && items.length > 0 && (
        <List>
          {items.map((item, index) => (
            <li key={index}>{item}</li>
          ))}
        </List>
      )}
    </Card>
  )
}

export default ServiceCard
