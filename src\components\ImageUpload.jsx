import { useState, useRef } from 'react';
import styled from 'styled-components';

const UploadContainer = styled.div`
  margin-bottom: 1.5rem;
`;

const UploadLabel = styled.label`
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
`;

const UploadButton = styled.button`
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: var(--primary-dark);
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const ImagePreview = styled.div`
  margin-top: 1rem;
  position: relative;
  display: inline-block;
`;

const PreviewImage = styled.img`
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
  border: 1px solid #ddd;
`;

const RemoveButton = styled.button`
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(255, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: rgba(255, 0, 0, 0.9);
  }
`;

const ProgressBar = styled.div`
  height: 5px;
  background-color: #f0f0f0;
  border-radius: 5px;
  margin-top: 0.5rem;
  overflow: hidden;
`;

const Progress = styled.div`
  height: 100%;
  background-color: var(--primary-color);
  width: ${props => props.value}%;
  transition: width 0.3s ease;
`;

const ErrorMessage = styled.div`
  color: red;
  margin-top: 0.5rem;
  font-size: 0.875rem;
`;

/**
 * Image upload component with preview and progress indicator
 */
function ImageUpload({ label, onChange, value, accept = "image/*", maxSize = 5 * 1024 * 1024 }) {
  const [preview, setPreview] = useState(value || '');
  const [error, setError] = useState('');
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const fileInputRef = useRef(null);

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Check file size
    if (file.size > maxSize) {
      setError(`File size exceeds the maximum allowed size (${Math.round(maxSize / 1024 / 1024)}MB)`);
      return;
    }

    setError('');
    setUploading(true);
    
    // Create a preview URL
    const previewUrl = URL.createObjectURL(file);
    setPreview(previewUrl);

    // Simulate upload progress
    let currentProgress = 0;
    const interval = setInterval(() => {
      currentProgress += 10;
      setProgress(currentProgress);
      
      if (currentProgress >= 100) {
        clearInterval(interval);
        setUploading(false);
        
        // In a real implementation, you would upload the file to your server or storage service here
        // and then call onChange with the URL returned from the server
        onChange(previewUrl);
      }
    }, 200);
  };

  const handleRemove = () => {
    setPreview('');
    setProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onChange('');
  };

  return (
    <UploadContainer>
      <UploadLabel>{label}</UploadLabel>
      
      <UploadButton 
        as="label" 
        htmlFor="file-upload"
        disabled={uploading}
      >
        {uploading ? 'Uploading...' : 'Choose File'}
      </UploadButton>
      
      <input
        id="file-upload"
        type="file"
        accept={accept}
        onChange={handleFileChange}
        style={{ display: 'none' }}
        ref={fileInputRef}
      />
      
      {uploading && (
        <ProgressBar>
          <Progress value={progress} />
        </ProgressBar>
      )}
      
      {error && <ErrorMessage>{error}</ErrorMessage>}
      
      {preview && (
        <ImagePreview>
          <PreviewImage src={preview} alt="Preview" />
          <RemoveButton onClick={handleRemove}>×</RemoveButton>
        </ImagePreview>
      )}
    </UploadContainer>
  );
}

export default ImageUpload;
