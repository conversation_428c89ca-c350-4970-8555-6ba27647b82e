// Custom configuration for Decap CMS
console.log("Loading custom CMS configuration");

// Wait for CMS to be ready
window.addEventListener('load', function() {
  // Make sure CMS is available
  if (window.CMS) {
    // Register a preview template for the home page
    CMS.registerPreviewTemplate('home', function(props) {
      console.log("Home preview props:", props);
      return createPreview(props);
    });
    
    // Register a preview template for the about page
    CMS.registerPreviewTemplate('about', function(props) {
      console.log("About preview props:", props);
      return createPreview(props);
    });
    
    // Log when the CMS is ready
    CMS.registerEventListener({
      name: 'onInit',
      handler: function() {
        console.log("CMS initialized successfully");
      }
    });
    
    // Log when content is loaded
    CMS.registerEventListener({
      name: 'preSave',
      handler: function(data) {
        console.log('Content about to be saved:', data);
      }
    });
    
    // Log when content is saved
    CMS.registerEventListener({
      name: 'postSave',
      handler: function(data) {
        console.log('Content saved:', data);
      }
    });
  } else {
    console.error("CMS not available");
  }
});

// Simple preview component
function createPreview(props) {
  const data = props.entry.get('data').toJS();
  console.log("Preview data:", data);
  
  // Create a preview container
  const container = document.createElement('div');
  container.className = 'preview-container';
  
  // Add title
  const title = document.createElement('h1');
  title.textContent = data.heroTitle || 'No title';
  container.appendChild(title);
  
  // Add subtitle
  const subtitle = document.createElement('p');
  subtitle.textContent = data.heroSubtitle || 'No subtitle';
  container.appendChild(subtitle);
  
  // Add a message about the preview
  const message = document.createElement('div');
  message.className = 'preview-message';
  message.textContent = 'This is a simple preview. The actual page will look different.';
  container.appendChild(message);
  
  return container;
}
